"""
Reference Style Processor for Options Analysis.
Replicates the exact approach from the reference project for pivot point calculations and CSV output.
"""

import logging
import pandas as pd
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from fyers_client import FyersClient
from config_loader import ConfigLoader
from universal_symbol_parser import UniversalSymbolParser
from black_scholes_calculator import BlackScholesCalculator

logger = logging.getLogger(__name__)

@dataclass
class OptionData:
    """Option data structure matching reference project."""
    strike: float
    expiry_date: str
    type: str  # CE or PE
    symbol: str
    LTP: float
    volume: int
    open: float
    high: float
    low: float
    close: float
    prev_close: float
    change: float
    change_percent: float
    delta: float
    pivots: Dict[str, float]

class ReferenceStyleProcessor:
    """Processor that replicates the reference project's exact approach."""
    
    def __init__(self, config: ConfigLoader):
        self.config = config
        self.fyers_client = None
        self.symbol_parser = UniversalSymbolParser(config, config.symbols)
        self.bs_calculator = BlackScholesCalculator()
        
    def authenticate_fyers(self) -> bool:
        """Authenticate with Fyers API."""
        try:
            from fyers_config import FyersConfig
            fyers_config = FyersConfig(env_path=self.config.env_path)
            self.fyers_client = FyersClient()
            self.fyers_client.config = fyers_config
            return self.fyers_client.authenticate()
        except Exception as e:
            logger.error(f"Failed to authenticate with Fyers: {e}")
            return False
    
    def _calculate_pivot_standard(self, high: float, low: float, close: float) -> Dict[str, float]:
        """
        Calculate pivot points using the exact formula from reference project.
        """
        def _align_to_nearest_5(x: float) -> float:
            """Rounds to the nearest value ending with .00 or .05."""
            sign = 1 if x >= 0 else -1
            abs_x = abs(x)
            nearest = round(abs_x * 20) / 20.0  # 0.05 steps
            return round(sign * nearest, 2)

        # Calculate pivot point
        P = (high + low + close) / 3

        # Resistance and support levels using extended formulas
        R1 = P * 2 - low
        S1 = P * 2 - high
        R2 = P + (high - low)
        S2 = P - (high - low)
        R3 = P * 2 + (high - 2 * low)
        S3 = P * 2 - (2 * high - low)
        R4 = P * 3 + (high - 3 * low)
        S4 = P * 3 - (3 * high - low)
        R5 = P * 4 + (high - 4 * low)
        S5 = P * 4 - (4 * high - low)

        R6 = P * 5 + (high - 5 * low)
        S6 = P * 5 - (5 * high - low)
        R7 = P * 6 + (high - 6 * low)
        S7 = P * 6 - (6 * high - low)
        R8 = P * 7 + (high - 7 * low)
        S8 = P * 7 - (7 * high - low)
        R9 = P * 8 + (high - 8 * low)
        S9 = P * 8 - (8 * high - low)
        R10 = P * 9 + (high - 9 * low)
        S10 = P * 9 - (9 * high - low)
        R11 = P * 10 + (high - 10 * low)
        S11 = P * 10 - (10 * high - low)

        return {
            'Pivot': _align_to_nearest_5(round(P, 2)),
            'R1': _align_to_nearest_5(round(R1, 2)),
            'S1': _align_to_nearest_5(round(S1, 2)),
            'R2': _align_to_nearest_5(round(R2, 2)),
            'S2': _align_to_nearest_5(round(S2, 2)),
            'R3': _align_to_nearest_5(round(R3, 2)),
            'S3': _align_to_nearest_5(round(S3, 2)),
            'R4': _align_to_nearest_5(round(R4, 2)),
            'S4': _align_to_nearest_5(round(S4, 2)),
            'R5': _align_to_nearest_5(round(R5, 2)),
            'S5': _align_to_nearest_5(round(S5, 2)),
            'R6': _align_to_nearest_5(round(R6, 2)),
            'S6': _align_to_nearest_5(round(S6, 2)),
            'R7': _align_to_nearest_5(round(R7, 2)),
            'S7': _align_to_nearest_5(round(S7, 2)),
            'R8': _align_to_nearest_5(round(R8, 2)),
            'S8': _align_to_nearest_5(round(S8, 2)),
            'R9': _align_to_nearest_5(round(R9, 2)),
            'S9': _align_to_nearest_5(round(S9, 2)),
            'R10': _align_to_nearest_5(round(R10, 2)),
            'S10': _align_to_nearest_5(round(S10, 2)),
            'R11': _align_to_nearest_5(round(R11, 2)),
            'S11': _align_to_nearest_5(round(S11, 2)),
        }

    def get_option_chain_for_symbol(self, symbol: str) -> List[OptionData]:
        """Get option chain for a symbol with delta filtering and pivot calculations."""
        if not self.fyers_client:
            logger.error("Fyers client not authenticated")
            return []
        
        try:
            # Get spot price using the same method as current project
            from options_chain_filter import OptionsChainFilter
            options_filter = OptionsChainFilter(self.config)
            spot_prices = options_filter.get_spot_prices_for_underlyings([symbol])
            spot_price = spot_prices.get(symbol, 0.0)

            if spot_price <= 0:
                logger.error(f"Could not get spot price for {symbol}")
                return []

            logger.info(f"Spot price for {symbol}: {spot_price}")
            
            # Get options symbols for current month (like reference project)
            csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
            all_options = self.symbol_parser.load_symbols_from_csv(csv_file, ['OPTIONS'])
            
            # Filter for current symbol and current month only
            current_month_options = []
            current_date = datetime.now()
            current_year = current_date.year % 100  # Get 2-digit year
            current_month_name = current_date.strftime('%b').upper()

            logger.info(f"Looking for options with year={current_year}, month={current_month_name}")

            # Debug: Check what options we have
            symbol_options = [opt for opt in all_options if opt.underlying == symbol]
            logger.info(f"Found {len(symbol_options)} total options for {symbol}")

            if symbol_options:
                # Show sample of available expiries
                sample_expiries = set((opt.expiry_year, opt.expiry_month) for opt in symbol_options[:10])
                logger.info(f"Sample expiries available: {sample_expiries}")

            for option in all_options:
                if (option.underlying == symbol and
                    str(option.expiry_year) == str(current_year) and
                    option.expiry_month == current_month_name):
                    current_month_options.append(option)
            
            logger.info(f"Found {len(current_month_options)} current month options for {symbol}")
            
            if not current_month_options:
                return []
            
            # Apply delta filtering before fetching market data (like reference project)
            delta_filtered_options = self._apply_delta_filter_to_options(current_month_options, spot_price)
            logger.info(f"Delta filtering: {len(delta_filtered_options)}/{len(current_month_options)} options passed")
            
            if not delta_filtered_options:
                return []
            
            # Fetch market data for filtered options
            option_symbols = [opt.get_nse_symbol() for opt in delta_filtered_options]
            market_data = self.fyers_client.get_quotes(option_symbols)
            
            if not market_data:
                logger.warning("No market data received for options")
                return []
            
            # Process options and calculate pivots
            processed_options = []
            for option in delta_filtered_options:
                symbol_str = option.get_nse_symbol()
                if symbol_str in market_data:
                    data = market_data[symbol_str]
                    
                    # Fetch weekly OHLC for pivot calculation (like reference project)
                    ohlc_data = self.fyers_client.get_historical_data(symbol_str, '1D', 7)  # Weekly data
                    
                    if ohlc_data and len(ohlc_data) >= 5:
                        # Calculate pivot points using weekly OHLC
                        high = max(d.high for d in ohlc_data[-5:])  # Last 5 days high
                        low = min(d.low for d in ohlc_data[-5:])    # Last 5 days low
                        close = ohlc_data[-1].close                 # Last close
                        
                        pivots = self._calculate_pivot_standard(high, low, close)
                        
                        # Calculate accurate delta using Black-Scholes
                        expiry_day = self._extract_expiry_day_from_symbol(option.symbol)
                        expiry_str = f"{expiry_day}{option.expiry_month}"
                        estimated_delta = self.bs_calculator.calculate_accurate_delta(
                            symbol_str, data.ltp, spot_price, option.strike_price, expiry_str, option.option_type
                        )
                        
                        option_data = OptionData(
                            strike=option.strike_price,
                            expiry_date=f"{option.expiry_day}{option.expiry_month}",
                            type=option.option_type,
                            symbol=symbol_str,
                            LTP=data.ltp,
                            volume=data.volume,
                            open=data.open,
                            high=data.high,
                            low=data.low,
                            close=data.close,
                            prev_close=data.prev_close,
                            change=data.change,
                            change_percent=data.change_percent,
                            delta=estimated_delta,
                            pivots=pivots
                        )
                        processed_options.append(option_data)
            
            logger.info(f"Processed {len(processed_options)} options with pivot calculations")
            return processed_options
            
        except Exception as e:
            logger.error(f"Error getting option chain for {symbol}: {e}")
            return []

    def _apply_delta_filter_to_options(self, options: List, spot_price: float) -> List:
        """Apply delta filtering to options before fetching market data using Black-Scholes."""
        min_delta = self.config.options_min_delta
        max_delta = self.config.options_max_delta

        if min_delta == 0.0 and max_delta == 1.0:
            return options

        filtered_options = []
        for option in options:
            try:
                # Extract expiry day from symbol (e.g., NIFTY25JUL17... -> day=17)
                expiry_day = self._extract_expiry_day_from_symbol(option.symbol)
                expiry_str = f"{expiry_day}{option.expiry_month}"

                # For prefiltering, we'll use a simplified approach with default IV
                # since we don't have market prices yet
                T = self.bs_calculator.calculate_time_to_expiration(expiry_str)
                estimated_delta = abs(self.bs_calculator.black_scholes_delta(
                    spot_price, option.strike_price, T, 0.05, 0.2, option.option_type
                ))

                if min_delta <= estimated_delta <= max_delta:
                    filtered_options.append(option)
                    logger.debug(f"Delta prefilter passed for {option.get_nse_symbol()}: delta={estimated_delta:.3f}")
                else:
                    logger.debug(f"Delta prefilter failed for {option.get_nse_symbol()}: delta={estimated_delta:.3f}")

            except Exception as e:
                logger.warning(f"Error in delta prefiltering for {option.get_nse_symbol()}: {e}")
                # Include option if delta calculation fails
                filtered_options.append(option)

        return filtered_options

    def _extract_expiry_day_from_symbol(self, symbol: str) -> str:
        """
        Extract expiry day from option symbol.
        E.g., NIFTY25JUL17... -> returns '17'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol.replace('NSE:', '')

            # Find the month part (3 letters after year)
            # Pattern: NIFTY25JUL17... -> we want the '17' part
            import re
            match = re.search(r'(\d{2})([A-Z]{3})(\d{2})', clean_symbol)
            if match:
                return match.group(3)  # Return the day part
            else:
                # Fallback: assume current month's last Thursday (typical NIFTY expiry)
                from datetime import datetime
                current_date = datetime.now()
                return f"{current_date.day:02d}"

        except Exception as e:
            logger.warning(f"Error extracting expiry day from {symbol}: {e}")
            return "25"  # Default to 25th (typical monthly expiry)

    def generate_reference_style_reports(self, symbol: str, options_data: List[OptionData]) -> Dict[str, str]:
        """Generate reports in the exact format as reference project."""
        if not options_data:
            logger.warning(f"No options data to generate reports for {symbol}")
            return {}

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        reports_dir = self.config.output_dir

        if not os.path.exists(reports_dir):
            os.makedirs(reports_dir)

        # Report 1: All filtered options with pivots (like filtered_pivot_SYMBOL_timestamp.csv)
        all_options_df = self._create_all_options_dataframe(options_data)
        all_filename = f"filtered_pivot_{symbol}_{timestamp}.csv"
        all_filepath = os.path.join(reports_dir, all_filename)
        all_options_df.to_csv(all_filepath, index=False)
        logger.info(f"Generated filtered pivot report: {all_filepath}")

        # Report 2: Top N closest to positive pivots (like closest_positive_pivots_SYMBOL_timestamp.csv)
        closest_options = self._find_closest_to_positive_pivots(options_data)
        if closest_options:
            closest_df = self._create_closest_pivots_dataframe(closest_options[:5])  # Top 5
            closest_filename = f"closest_positive_pivots_{symbol}_{timestamp}.csv"
            closest_filepath = os.path.join(reports_dir, closest_filename)
            closest_df.to_csv(closest_filepath, index=False)
            logger.info(f"Generated closest positive pivots report: {closest_filepath}")

        # Report 3: Final shortlisted (like final_shortlisted_SYMBOL_timestamp.csv)
        final_shortlisted = self._find_final_shortlisted(closest_options if closest_options else [])
        if final_shortlisted:
            final_df = self._create_final_shortlisted_dataframe(final_shortlisted)
            final_filename = f"final_shortlisted_{symbol}_{timestamp}.csv"
            final_filepath = os.path.join(reports_dir, final_filename)
            final_df.to_csv(final_filepath, index=False)
            logger.info(f"Generated final shortlisted report: {final_filepath}")

            # Display final results like reference project
            self._display_final_results(symbol, final_shortlisted)

        return {
            'filtered_pivot': all_filepath,
            'closest_positive_pivots': closest_filepath if closest_options else None,
            'final_shortlisted': final_filepath if final_shortlisted else None
        }

    def _create_all_options_dataframe(self, options_data: List[OptionData]) -> pd.DataFrame:
        """Create dataframe for all options with pivot columns expanded."""
        rows = []
        for option in options_data:
            row = {
                'strike': option.strike,
                'expiry_date': option.expiry_date,
                'type': option.type,
                'symbol': option.symbol,
                'LTP': option.LTP,
                'volume': option.volume,
                'open': option.open,
                'high': option.high,
                'low': option.low,
                'close': option.close,
                'prev_close': option.prev_close,
                'change': option.change,
                'change_percent': option.change_percent,
                'delta': option.delta,
            }
            # Add pivot columns
            row.update(option.pivots)
            rows.append(row)

        return pd.DataFrame(rows)

    def _find_closest_to_positive_pivots(self, options_data: List[OptionData]) -> List[Dict[str, Any]]:
        """Find options closest to their minimum positive pivot levels."""
        results = []

        for option in options_data:
            # Find minimum positive pivot
            positive_pivots = {k: v for k, v in option.pivots.items() if v > 0}

            if positive_pivots:
                min_positive_pivot = min(positive_pivots.values())
                min_pivot_level = None
                for level, value in positive_pivots.items():
                    if value == min_positive_pivot:
                        min_pivot_level = level
                        break

                distance = abs(option.LTP - min_positive_pivot)
                distance_percent = (distance / min_positive_pivot) * 100

                result = {
                    'option': option,
                    'min_positive_pivot_level': min_pivot_level,
                    'min_positive_pivot_value': min_positive_pivot,
                    'distance_to_min_positive_pivot': distance,
                    'distance_to_min_positive_pivot_pct': distance_percent
                }
                results.append(result)

        # Sort by distance to minimum positive pivot
        results.sort(key=lambda x: x['distance_to_min_positive_pivot'])
        return results

    def _find_final_shortlisted(self, closest_options: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Find final shortlisted options (those with minimum distance)."""
        if not closest_options:
            return []

        min_distance = closest_options[0]['distance_to_min_positive_pivot']
        final_shortlisted = [opt for opt in closest_options if opt['distance_to_min_positive_pivot'] == min_distance]
        return final_shortlisted

    def _create_closest_pivots_dataframe(self, closest_options: List[Dict[str, Any]]) -> pd.DataFrame:
        """Create dataframe for closest positive pivots report."""
        rows = []
        for item in closest_options:
            option = item['option']
            row = {
                'strike': option.strike,
                'expiry_date': option.expiry_date,
                'type': option.type,
                'symbol': option.symbol,
                'LTP': option.LTP,
                'volume': option.volume,
                'open': option.open,
                'high': option.high,
                'low': option.low,
                'close': option.close,
                'prev_close': option.prev_close,
                'change': option.change,
                'change_percent': option.change_percent,
                'delta': option.delta,
                'min_positive_pivot_level': item['min_positive_pivot_level'],
                'min_positive_pivot_value': item['min_positive_pivot_value'],
                'distance_to_min_positive_pivot': item['distance_to_min_positive_pivot'],
                'distance_to_min_positive_pivot_pct': item['distance_to_min_positive_pivot_pct']
            }
            # Add pivot columns
            row.update(option.pivots)
            rows.append(row)

        return pd.DataFrame(rows)

    def _create_final_shortlisted_dataframe(self, final_options: List[Dict[str, Any]]) -> pd.DataFrame:
        """Create dataframe for final shortlisted report."""
        return self._create_closest_pivots_dataframe(final_options)

    def _display_final_results(self, symbol: str, final_shortlisted: List[Dict[str, Any]]):
        """Display final results like reference project."""
        logger.info(f"Final shortlisted option(s) (LTP closest to minimum positive pivot levels) for {symbol}: {len(final_shortlisted)}")
        logger.info("=" * 150)

        for item in final_shortlisted:
            option = item['option']
            logger.info(f"Symbol: {option.symbol}, Type: {option.type}, Strike: {option.strike}, "
                       f"LTP: {option.LTP:.2f}, Min Positive Pivot Level: {item['min_positive_pivot_level']}, "
                       f"Min Positive Pivot Value: {item['min_positive_pivot_value']:.2f}, "
                       f"Distance: {item['distance_to_min_positive_pivot']:.2f}, "
                       f"Distance %: {item['distance_to_min_positive_pivot_pct']:.2f}")

        logger.info("=" * 150)

    def process_symbol(self, symbol: str) -> Dict[str, str]:
        """Process a symbol and generate reference-style reports."""
        logger.info(f"--- Starting processing for symbol: {symbol} ---")

        if not self.authenticate_fyers():
            logger.error("Fyers authentication failed")
            return {}

        # Get option chain with delta filtering and pivot calculations
        options_data = self.get_option_chain_for_symbol(symbol)

        if not options_data:
            logger.warning(f"No options data found for {symbol}")
            return {}

        # Generate reports
        reports = self.generate_reference_style_reports(symbol, options_data)

        logger.info(f"--- Finished processing for symbol: {symbol} ---")
        return reports
