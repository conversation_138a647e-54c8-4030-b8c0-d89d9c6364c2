"""
Pivot Point Indicator for Multi-Market Type Scanner.
Calculates pivot points (P, R1-R11, S1-S11) for DAILY, WEEKLY, MONTHLY timeframes.
Finds symbols trading closest to pivot point levels.
"""

import logging
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class PivotPointLevels:
    """Pivot point levels for a symbol."""
    pivot: float
    resistance_levels: Dict[str, float]  # R1-R11
    support_levels: Dict[str, float]     # S1-S11
    calculation_type: str                # DAILY, WEEKLY, MONTHLY
    calculation_date: str                # Date used for calculation

@dataclass
class PivotPointAnalysis:
    """Analysis result for a symbol's proximity to pivot points."""
    symbol: str
    current_ltp: float
    pivot_levels: PivotPointLevels
    closest_pivot_level: str            # e.g., "R1", "S2", "P"
    closest_pivot_value: float
    distance_to_closest: float          # Absolute distance
    distance_percent: float             # Percentage distance
    is_positive_minimum: bool           # True if this is the minimum positive distance
    top_n_closest: List[Dict[str, Any]] # Top N closest pivot levels

class PivotPointAnalyzer:
    """Analyzer for Pivot Point calculations and proximity analysis."""
    
    def __init__(self, calculation_type: str = 'WEEKLY', top_n_closest: int = 5):
        """
        Initialize the Pivot Point Analyzer.
        
        Args:
            calculation_type: 'DAILY', 'WEEKLY', 'MONTHLY'
            top_n_closest: Number of closest pivot points to consider
        """
        self.calculation_type = calculation_type.upper()
        self.top_n_closest = top_n_closest
        
        if self.calculation_type not in ['DAILY', 'WEEKLY', 'MONTHLY']:
            raise ValueError(f"Invalid calculation_type: {calculation_type}. Must be DAILY, WEEKLY, or MONTHLY")
        
        logger.info(f"Pivot Point Analyzer initialized with {self.calculation_type} calculation")

    def _align_to_nearest_5(self, x: float) -> float:
        """
        Rounds to the nearest value ending with .00 or .05 (up or down as per closest).
        E.g., 44.03->44.05, 69.97->69.95, 17.92->17.90, 96.08->96.10, -8.02->-8.00, 122.02->122.00, -34.13->-34.15, 147.95->147.95, -60.25->-60.25, 173.88->173.90, -86.37->-86.35
        """
        sign = 1 if x >= 0 else -1
        abs_x = abs(x)
        int_part = int(abs_x)
        decimal = abs_x - int_part
        # Find the nearest .00 or .05
        lower = int_part + (0.00 if decimal < 0.025 else 0.05)
        upper = int_part + (0.05 if decimal < 0.05 else 0.10)
        # Find the two candidates
        candidate1 = int_part + (round(decimal / 0.05) * 0.05)
        candidate2 = int_part + (int(decimal / 0.05) * 0.05)
        # Always round to nearest .00 or .05
        nearest = round(abs_x * 20) / 20.0  # 0.05 steps
        # Now, round to 2 decimals and restore sign
        return round(sign * nearest, 2)

    def calculate_pivot_points(self, high: float, low: float, close: float) -> PivotPointLevels:
        """
        Calculate pivot points using the exact formula from reference project.

        Args:
            high: High price of the period
            low: Low price of the period
            close: Close price of the period

        Returns:
            PivotPointLevels object with all calculated levels
        """
        try:
            # Calculate pivot point
            P = (high + low + close) / 3

            # Resistance and support levels using extended formulas from reference project
            R1 = P * 2 - low
            S1 = P * 2 - high
            R2 = P + (high - low)
            S2 = P - (high - low)
            R3 = P * 2 + (high - 2 * low)
            S3 = P * 2 - (2 * high - low)
            R4 = P * 3 + (high - 3 * low)
            S4 = P * 3 - (3 * high - low)
            R5 = P * 4 + (high - 4 * low)
            S5 = P * 4 - (4 * high - low)

            R6 = P * 5 + (high - 5 * low)
            S6 = P * 5 - (5 * high - low)
            R7 = P * 6 + (high - 6 * low)
            S7 = P * 6 - (6 * high - low)
            R8 = P * 7 + (high - 7 * low)
            S8 = P * 7 - (7 * high - low)
            R9 = P * 8 + (high - 8 * low)
            S9 = P * 8 - (8 * high - low)
            R10 = P * 9 + (high - 9 * low)
            S10 = P * 9 - (9 * high - low)
            R11 = P * 10 + (high - 10 * low)
            S11 = P * 10 - (10 * high - low)

            # Apply alignment to nearest 0.05 and create levels dictionaries
            resistance_levels = {
                'R1': self._align_to_nearest_5(round(R1, 2)),
                'R2': self._align_to_nearest_5(round(R2, 2)),
                'R3': self._align_to_nearest_5(round(R3, 2)),
                'R4': self._align_to_nearest_5(round(R4, 2)),
                'R5': self._align_to_nearest_5(round(R5, 2)),
                'R6': self._align_to_nearest_5(round(R6, 2)),
                'R7': self._align_to_nearest_5(round(R7, 2)),
                'R8': self._align_to_nearest_5(round(R8, 2)),
                'R9': self._align_to_nearest_5(round(R9, 2)),
                'R10': self._align_to_nearest_5(round(R10, 2)),
                'R11': self._align_to_nearest_5(round(R11, 2)),
            }

            support_levels = {
                'S1': self._align_to_nearest_5(round(S1, 2)),
                'S2': self._align_to_nearest_5(round(S2, 2)),
                'S3': self._align_to_nearest_5(round(S3, 2)),
                'S4': self._align_to_nearest_5(round(S4, 2)),
                'S5': self._align_to_nearest_5(round(S5, 2)),
                'S6': self._align_to_nearest_5(round(S6, 2)),
                'S7': self._align_to_nearest_5(round(S7, 2)),
                'S8': self._align_to_nearest_5(round(S8, 2)),
                'S9': self._align_to_nearest_5(round(S9, 2)),
                'S10': self._align_to_nearest_5(round(S10, 2)),
                'S11': self._align_to_nearest_5(round(S11, 2)),
            }

            calculation_date = datetime.now().strftime('%Y-%m-%d')

            return PivotPointLevels(
                pivot=self._align_to_nearest_5(round(P, 2)),
                resistance_levels=resistance_levels,
                support_levels=support_levels,
                calculation_type=self.calculation_type,
                calculation_date=calculation_date
            )
            
        except Exception as e:
            logger.error(f"Error calculating pivot points: {e}")
            raise
    
    def get_period_data_for_calculation(self, ohlc_data: List[Any]) -> Tuple[float, float, float]:
        """
        Extract high, low, close data for the specified calculation period.
        
        Args:
            ohlc_data: List of OHLC data objects
            
        Returns:
            Tuple of (high, low, close) for the calculation period
        """
        try:
            if not ohlc_data:
                raise ValueError("No OHLC data provided")
            
            df = pd.DataFrame([{
                'high': getattr(row, 'high', 0),
                'low': getattr(row, 'low', 0),
                'close': getattr(row, 'close', 0),
                'datetime': getattr(row, 'datetime', datetime.now())
            } for row in ohlc_data])
            
            if df.empty:
                raise ValueError("Empty OHLC dataframe")
            
            # Convert datetime column if it's not already datetime
            if not pd.api.types.is_datetime64_any_dtype(df['datetime']):
                df['datetime'] = pd.to_datetime(df['datetime'])
            
            # Sort by datetime to ensure proper ordering
            df = df.sort_values('datetime')
            
            if self.calculation_type == 'DAILY':
                # Use the last complete day's data
                high = df['high'].iloc[-1]
                low = df['low'].iloc[-1]
                close = df['close'].iloc[-1]
            
            elif self.calculation_type == 'WEEKLY':
                # Use the last week's data (last 5-7 trading days)
                recent_data = df.tail(7)  # Get last 7 periods
                high = recent_data['high'].max()
                low = recent_data['low'].min()
                close = recent_data['close'].iloc[-1]
            
            elif self.calculation_type == 'MONTHLY':
                # Use the last month's data (last 20-22 trading days)
                recent_data = df.tail(22)  # Get last 22 periods
                high = recent_data['high'].max()
                low = recent_data['low'].min()
                close = recent_data['close'].iloc[-1]
            
            return float(high), float(low), float(close)
            
        except Exception as e:
            logger.error(f"Error extracting period data: {e}")
            raise
    
    def analyze_symbol_proximity_to_pivots(self, symbol: str, current_ltp: float, 
                                         ohlc_data: List[Any]) -> Optional[PivotPointAnalysis]:
        """
        Analyze a symbol's proximity to pivot point levels.
        
        Args:
            symbol: Symbol name
            current_ltp: Current Last Traded Price
            ohlc_data: Historical OHLC data
            
        Returns:
            PivotPointAnalysis object or None if analysis fails
        """
        try:
            # Get period data for calculation
            high, low, close = self.get_period_data_for_calculation(ohlc_data)
            
            # Calculate pivot points
            pivot_levels = self.calculate_pivot_points(high, low, close)
            
            # Create a dictionary of all pivot levels
            all_levels = {'P': pivot_levels.pivot}
            all_levels.update(pivot_levels.resistance_levels)
            all_levels.update(pivot_levels.support_levels)
            
            # Calculate distances to all pivot levels
            distances = []
            for level_name, level_value in all_levels.items():
                distance = abs(current_ltp - level_value)
                distance_percent = (distance / level_value) * 100 if level_value != 0 else float('inf')
                
                distances.append({
                    'level_name': level_name,
                    'level_value': level_value,
                    'distance': distance,
                    'distance_percent': distance_percent
                })
            
            # Sort by distance (closest first)
            distances.sort(key=lambda x: x['distance'])
            
            # Get closest pivot level
            closest = distances[0]
            
            # Get top N closest levels
            top_n_closest = distances[:self.top_n_closest]
            
            # Check if this is the minimum positive distance
            positive_distances = [d for d in distances if d['distance'] > 0]
            is_positive_minimum = len(positive_distances) > 0 and closest['distance'] == positive_distances[0]['distance']
            
            return PivotPointAnalysis(
                symbol=symbol,
                current_ltp=current_ltp,
                pivot_levels=pivot_levels,
                closest_pivot_level=closest['level_name'],
                closest_pivot_value=closest['level_value'],
                distance_to_closest=closest['distance'],
                distance_percent=closest['distance_percent'],
                is_positive_minimum=is_positive_minimum,
                top_n_closest=top_n_closest
            )
            
        except Exception as e:
            logger.error(f"Error analyzing pivot proximity for {symbol}: {e}")
            return None
    
    def find_symbols_closest_to_pivots(self, symbol_analyses: List[PivotPointAnalysis]) -> List[PivotPointAnalysis]:
        """
        Find symbols that are trading closest to pivot point levels.
        
        Args:
            symbol_analyses: List of PivotPointAnalysis objects
            
        Returns:
            List of symbols closest to their respective pivot levels
        """
        try:
            if not symbol_analyses:
                return []
            
            # Sort by distance to closest pivot (ascending)
            sorted_analyses = sorted(symbol_analyses, key=lambda x: x.distance_to_closest)
            
            # Return top N closest symbols
            return sorted_analyses[:self.top_n_closest]
            
        except Exception as e:
            logger.error(f"Error finding closest symbols: {e}")
            return []
    
    def find_symbols_at_minimum_positive_pivots(self, symbol_analyses: List[PivotPointAnalysis]) -> List[PivotPointAnalysis]:
        """
        Find symbols trading at minimum positive pivot point levels.
        
        Args:
            symbol_analyses: List of PivotPointAnalysis objects
            
        Returns:
            List of symbols at minimum positive pivot levels
        """
        try:
            # Filter symbols that are at their minimum positive pivot distance
            minimum_positive_symbols = [analysis for analysis in symbol_analyses 
                                      if analysis.is_positive_minimum and analysis.distance_to_closest > 0]
            
            # Sort by distance (closest first)
            minimum_positive_symbols.sort(key=lambda x: x.distance_to_closest)
            
            logger.info(f"Found {len(minimum_positive_symbols)} symbols at minimum positive pivot levels")
            
            return minimum_positive_symbols
            
        except Exception as e:
            logger.error(f"Error finding minimum positive pivot symbols: {e}")
            return []
