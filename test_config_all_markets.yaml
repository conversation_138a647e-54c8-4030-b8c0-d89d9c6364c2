# Test Configuration for All Market Types
general:
  env_path: '../.env'
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

market_types:
  - EQUITY
  - INDEX
  - FUTURES
  - OPTIONS

# Trading Symbols - Test with specific symbols
symbols:
  - 'NIFTY'
  - 'BANKNIFTY'
  - 'RELIANCE'

# Market Filter Settings
market_filters:
  min_volume: 0
  max_volume: ***********
  min_ltp_price: 0
  max_ltp_price: 100000.0

# Options Filter Settings
options_filter:
  strike_level: 20    # Reduced for testing

# Timeframe Settings
timeframe:
  interval: 60
  days_to_fetch: 10

ce_pe_pairing:
  enabled: true
  min_price_percent: 0.0
  max_price_percent: 2.0

# Moving Average Exponential Indicator Settings
mae_indicator:
  enabled: true
  length: 9
  source: 'close'
  offset: 0
  smoothing_enabled: false
  smoothing_line: 'sma'
  smoothing_length: 9

# Rate Limiting Settings
rate_limit:
  min_delay_seconds: 0.1
  max_retries: 5
  retry_backoff: 3.0
