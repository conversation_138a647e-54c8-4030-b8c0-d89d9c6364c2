"""
Test script for Multi-Market Type Scanner.
Tests all four market types (EQUITY, INDEX, FUTURES, OPTIONS) with various configurations.
"""

import os
import sys
import logging
import tempfile
import shutil
from typing import List, Dict

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_loader import Config<PERSON>oader
from universal_symbol_parser import UniversalSymbolParser
from market_type_scanner import MarketTypeScannerFactory
from options_chain_filter import OptionsChainFilter
from symbol_downloader import download_symbols_from_urls

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MultiMarketScannerTest:
    """Test class for multi-market scanner functionality."""
    
    def __init__(self):
        """Initialize test environment."""
        self.config = ConfigLoader("config.yaml")
        self.test_results = {}
        
    def test_config_loader(self) -> bool:
        """Test configuration loader functionality."""
        try:
            logger.info("Testing configuration loader...")
            
            # Test market types
            market_types = self.config.get_enabled_market_types()
            assert market_types, "No market types configured"
            logger.info(f"Market types: {market_types}")
            
            # Test CSV file mapping
            for market_type in market_types:
                csv_file = self.config.get_csv_file_for_market_type(market_type)
                logger.info(f"{market_type} -> {csv_file}")
            
            # Test fyers API URLs
            urls = self.config.fyers_api_url
            assert isinstance(urls, list), "fyers_api_url should be a list"
            logger.info(f"API URLs: {urls}")
            
            # Test options configuration
            strike_level = self.config.options_strike_level
            logger.info(f"Options strike level: {strike_level}")
            
            logger.info("✓ Configuration loader tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Configuration loader test failed: {e}")
            return False
    
    def test_symbol_downloader(self) -> bool:
        """Test symbol downloader with multiple URLs."""
        try:
            logger.info("Testing symbol downloader...")
            
            # Test with configured URLs
            urls = self.config.fyers_api_url
            results = download_symbols_from_urls(urls)
            
            # Check results
            success_count = sum(1 for success in results.values() if success)
            total_count = len(results)
            
            logger.info(f"Download results: {success_count}/{total_count} successful")
            
            # Check if files exist
            for url in urls:
                if 'NSE_CM' in url:
                    assert os.path.exists('NSE_CM.csv'), "NSE_CM.csv not found"
                elif 'NSE_FO' in url:
                    assert os.path.exists('NSE_FO.csv'), "NSE_FO.csv not found"
            
            logger.info("✓ Symbol downloader tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Symbol downloader test failed: {e}")
            return False
    
    def test_universal_symbol_parser(self) -> bool:
        """Test universal symbol parser for all market types."""
        try:
            logger.info("Testing universal symbol parser...")
            
            # Test with sample symbols
            test_symbols = [
                ('NSE:RELIANCE-EQ', 'NSE_CM.csv', 'EQUITY'),
                ('NSE:NIFTY50-INDEX', 'NSE_CM.csv', 'INDEX'),
                ('NSE:RELIANCE25JULFUT', 'NSE_FO.csv', 'FUTURES'),
                ('NSE:RELIANCE25JUL2500CE', 'NSE_FO.csv', 'OPTIONS'),
                ('NSE:NIFTY25JUL24000PE', 'NSE_FO.csv', 'OPTIONS')
            ]
            
            parser = UniversalSymbolParser(self.config, ['ALL'])
            
            for symbol, csv_file, expected_type in test_symbols:
                parsed = parser.parse_symbol(symbol, csv_file)
                if parsed:
                    assert parsed.market_type == expected_type, f"Expected {expected_type}, got {parsed.market_type}"
                    logger.info(f"✓ Parsed {symbol} -> {parsed.market_type}: {parsed.underlying}")
                else:
                    logger.warning(f"Could not parse {symbol}")
            
            logger.info("✓ Universal symbol parser tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Universal symbol parser test failed: {e}")
            return False
    
    def test_options_chain_filter(self) -> bool:
        """Test options chain filter functionality."""
        try:
            logger.info("Testing options chain filter...")
            
            # Create sample options symbols
            from universal_symbol_parser import UniversalSymbol
            
            sample_options = [
                UniversalSymbol('NIFTY25JUL24000CE', 'OPTIONS', 'NIFTY', expiry_year='25', 
                               expiry_month='JUL', strike_price=24000.0, option_type='CE'),
                UniversalSymbol('NIFTY25JUL24000PE', 'OPTIONS', 'NIFTY', expiry_year='25', 
                               expiry_month='JUL', strike_price=24000.0, option_type='PE'),
                UniversalSymbol('NIFTY25JUL24050CE', 'OPTIONS', 'NIFTY', expiry_year='25', 
                               expiry_month='JUL', strike_price=24050.0, option_type='CE'),
                UniversalSymbol('NIFTY25JUL24050PE', 'OPTIONS', 'NIFTY', expiry_year='25', 
                               expiry_month='JUL', strike_price=24050.0, option_type='PE'),
            ]
            
            options_filter = OptionsChainFilter(self.config)
            
            # Test strike multiplier detection
            multiplier = options_filter.detect_strike_multiplier(sample_options)
            logger.info(f"Detected strike multiplier: {multiplier}")
            
            # Test ATM estimation
            atm_strike = options_filter.estimate_atm_strike(sample_options, 24025.0)
            logger.info(f"Estimated ATM strike: {atm_strike}")
            
            # Test options chain creation
            chains = options_filter.create_options_chain('NIFTY', sample_options, 24025.0)
            logger.info(f"Created {len(chains)} options chains")
            
            logger.info("✓ Options chain filter tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Options chain filter test failed: {e}")
            return False
    
    def test_market_type_scanners(self) -> bool:
        """Test market type scanner factory."""
        try:
            logger.info("Testing market type scanners...")
            
            # Test scanner creation for each market type
            for market_type in self.config.get_enabled_market_types():
                try:
                    scanner = MarketTypeScannerFactory.create_scanner(market_type, self.config)
                    logger.info(f"✓ Created {market_type} scanner: {type(scanner).__name__}")
                    
                    # Test symbol loading (without authentication)
                    symbols = scanner.get_symbols_for_scanning(['RELIANCE', 'NIFTY'])
                    logger.info(f"  Found {len(symbols)} {market_type} symbols for test underlyings")
                    
                except Exception as e:
                    logger.error(f"✗ Failed to create {market_type} scanner: {e}")
                    return False
            
            logger.info("✓ Market type scanner tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Market type scanner test failed: {e}")
            return False
    
    def test_symbol_filtering(self) -> bool:
        """Test symbol filtering for specific symbols vs ALL."""
        try:
            logger.info("Testing symbol filtering...")
            
            # Test with specific symbols
            specific_symbols = ['RELIANCE', 'NIFTY']
            parser_specific = UniversalSymbolParser(self.config, specific_symbols)
            
            # Test with ALL symbols
            all_symbols = ['ALL']
            parser_all = UniversalSymbolParser(self.config, all_symbols)
            
            # Test equity symbols
            if os.path.exists('NSE_CM.csv'):
                equity_specific = parser_specific.get_symbols_for_market_type('EQUITY', specific_symbols)
                equity_all = parser_all.get_symbols_for_market_type('EQUITY')
                
                logger.info(f"Specific EQUITY symbols: {len(equity_specific)}")
                logger.info(f"ALL EQUITY symbols: {len(equity_all)}")
                
                assert len(equity_all) >= len(equity_specific), "ALL should have more or equal symbols"
            
            logger.info("✓ Symbol filtering tests passed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Symbol filtering test failed: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests."""
        logger.info("=" * 60)
        logger.info("STARTING MULTI-MARKET SCANNER TESTS")
        logger.info("=" * 60)
        
        tests = [
            ("Configuration Loader", self.test_config_loader),
            ("Symbol Downloader", self.test_symbol_downloader),
            ("Universal Symbol Parser", self.test_universal_symbol_parser),
            ("Options Chain Filter", self.test_options_chain_filter),
            ("Market Type Scanners", self.test_market_type_scanners),
            ("Symbol Filtering", self.test_symbol_filtering),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            logger.info(f"\nRunning {test_name} test...")
            try:
                if test_func():
                    passed += 1
                    self.test_results[test_name] = "PASSED"
                else:
                    self.test_results[test_name] = "FAILED"
            except Exception as e:
                logger.error(f"Test {test_name} failed with exception: {e}")
                self.test_results[test_name] = f"FAILED: {e}"
        
        # Print summary
        logger.info("=" * 60)
        logger.info("TEST SUMMARY")
        logger.info("=" * 60)
        
        for test_name, result in self.test_results.items():
            status = "✓" if result == "PASSED" else "✗"
            logger.info(f"{status} {test_name}: {result}")
        
        logger.info(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            logger.info("🎉 ALL TESTS PASSED!")
            return True
        else:
            logger.error(f"❌ {total - passed} tests failed")
            return False


def main():
    """Main test function."""
    try:
        # Change to script directory
        os.chdir(os.path.dirname(os.path.abspath(__file__)))
        
        # Run tests
        tester = MultiMarketScannerTest()
        success = tester.run_all_tests()
        
        return success
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
