"""
Market Type Scanner for Multi-Market Type Scanner.
Handles scanning of different market types (EQUITY, INDEX, FUTURES, OPTIONS).
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from dataclasses import dataclass
from collections import defaultdict

from config_loader import Config<PERSON><PERSON><PERSON>
from universal_symbol_parser import UniversalSymbolParser, UniversalSymbol
from options_chain_filter import OptionsChainFilter
from fyers_client import FyersClient
from technical_indicators import MAEAnalyzer
from pivot_point_indicator import PivotPointAnalyzer

logger = logging.getLogger(__name__)

@dataclass
class MarketData:
    """Market data for a symbol."""
    symbol: str
    ltp: float
    volume: int
    open_price: float = 0.0
    high_price: float = 0.0
    low_price: float = 0.0
    close_price: float = 0.0
    prev_close: float = 0.0
    change: float = 0.0
    change_percent: float = 0.0
    # MAE indicator data
    mae_value: Optional[float] = None
    # Pivot point indicator data
    pivot_analysis: Optional[Any] = None  # Will store PivotPointAnalysis object

@dataclass
class FilteredSymbol:
    """Filtered symbol with market data and metadata."""
    symbol: str
    underlying: str
    market_type: str
    market_data: MarketData

    # Market type specific fields
    suffix: Optional[str] = None
    expiry_year: Optional[str] = None
    expiry_month: Optional[str] = None
    strike_price: Optional[float] = None
    option_type: Optional[str] = None
    pair_id: Optional[str] = None  # For CE/PE pairing

    # Indicator data
    mae_value: Optional[float] = None
    pivot_analysis: Optional[Any] = None  # Will store PivotPointAnalysis object


class BaseMarketScanner(ABC):
    """Base class for market type scanners."""
    
    def __init__(self, config: ConfigLoader, market_type: str):
        """
        Initialize the base scanner.
        
        Args:
            config: Configuration loader instance
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
        """
        self.config = config
        self.market_type = market_type
        self.fyers_client = None
        
        # Initialize symbol parser
        self.symbol_parser = UniversalSymbolParser(config, config.symbols)
        
        logger.info(f"Initialized {market_type} scanner")
    
    def authenticate_fyers(self) -> bool:
        """Authenticate with Fyers API."""
        try:
            self.fyers_client = FyersClient(self.config.env_path)
            return self.fyers_client.authenticate()
        except Exception as e:
            logger.error(f"Failed to authenticate with Fyers: {e}")
            return False
    
    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning for this market type."""
        return self.symbol_parser.get_symbols_for_market_type(
            self.market_type, underlying_symbols
        )
    
    def fetch_market_data(self, symbols: List[str]) -> Dict[str, MarketData]:
        """Fetch market data for symbols with performance optimization."""
        if not self.fyers_client:
            logger.error("Fyers client not authenticated")
            return {}

        try:
            # Performance optimization: Use optimized method for large symbol lists
            if len(symbols) > 5000:
                logger.info(f"Using optimized fetching for {len(symbols)} symbols")
                market_data = self.fyers_client.get_quotes_optimized(symbols, chunk_size=2000)
            else:
                market_data = self.fyers_client.get_quotes(symbols)

            # Convert to MarketData objects (from fyers_client.MarketData to market_type_scanner.MarketData)
            converted_data = {}
            for symbol, fyers_data in market_data.items():
                converted_data[symbol] = MarketData(
                    symbol=symbol,
                    ltp=fyers_data.ltp,
                    volume=fyers_data.volume,
                    open_price=fyers_data.open_price,
                    high_price=fyers_data.high,
                    low_price=fyers_data.low,
                    close_price=fyers_data.close,
                    prev_close=fyers_data.prev_close,
                    change=fyers_data.change,
                    change_percent=fyers_data.change_percent
                )

            return converted_data
            
        except Exception as e:
            logger.error(f"Error fetching market data: {e}")
            return {}
    
    def apply_volume_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply volume filter. For INDEX market type, volume filtering is disabled."""
        # Skip volume filtering for INDEX market type
        if self.market_type == 'INDEX':
            logger.info(f"Volume filter skipped for {self.market_type} market type")
            return market_data

        filtered_data = {}

        for symbol, data in market_data.items():
            if self.config.min_volume <= data.volume <= self.config.max_volume:
                filtered_data[symbol] = data

        logger.info(f"Volume filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def apply_ltp_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply LTP filter."""
        filtered_data = {}
        
        for symbol, data in market_data.items():
            if self.config.min_ltp_price <= data.ltp <= self.config.max_ltp_price:
                filtered_data[symbol] = data
        
        logger.info(f"LTP filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data
    
    def update_ohlc_with_timeframe_data(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Update OHLC data to use the configured timeframe instead of current session data."""
        try:
            updated_data = {}
            
            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for the configured timeframe
                    logger.debug(f"Fetching timeframe OHLC data for {symbol} with interval {self.config.timeframe_interval} and days {self.config.days_to_fetch}")
                    ohlc_data = self.fyers_client.get_historical_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) > 0:
                        # Update OHLC data to use the last candle from historical data (timeframe-specific)
                        last_candle = ohlc_data[-1]
                        data.open_price = last_candle.open
                        data.high_price = last_candle.high
                        data.low_price = last_candle.low
                        data.close_price = last_candle.close
                        
                        # Recalculate change based on timeframe close vs previous close
                        if len(ohlc_data) > 1:
                            prev_candle = ohlc_data[-2]
                            data.prev_close = prev_candle.close
                            data.change = data.close_price - data.prev_close
                            data.change_percent = (data.change / data.prev_close * 100) if data.prev_close > 0 else 0
                        
                        logger.debug(f"Updated OHLC data for {symbol} to use {self.config.timeframe_interval}-minute timeframe data")
                    else:
                        logger.debug(f"No historical data available for {symbol}, keeping quotes data")
                    
                    updated_data[symbol] = data
                    
                except Exception as e:
                    logger.warning(f"Error updating OHLC data for {symbol}: {e}")
                    # Keep original data if there's an error
                    updated_data[symbol] = data

            logger.info(f"Updated OHLC data for {len(updated_data)} symbols to use {self.config.timeframe_interval}-minute timeframe")
            return updated_data
            
        except Exception as e:
            logger.error(f"Error updating OHLC data with timeframe: {e}")
            return market_data

    @abstractmethod
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply market type specific filters."""
        pass
    
    def apply_mae_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply MAE indicator filter if enabled."""
        if not self.config.mae_enabled:
            logger.debug("MAE indicator is disabled, skipping MAE filter")
            return market_data

        try:
            # Initialize MAE analyzer with config settings
            mae_analyzer = MAEAnalyzer(
                length=self.config.mae_length,
                source=self.config.mae_source,
                offset=self.config.mae_offset,
                smoothing_line=self.config.mae_smoothing_line,
                smoothing_length=self.config.mae_smoothing_length
            )

            filtered_data = {}
            mae_passed_count = 0

            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for MAE calculation
                    logger.info(f"Fetching OHLC data for {symbol} with interval {self.config.timeframe_interval} and days {self.config.days_to_fetch}")
                    ohlc_data = self.fyers_client.get_historical_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) >= self.config.mae_length:
                        # Calculate MAE and check if price is passing through
                        use_smoothed = getattr(self.config, 'mae_smoothing_enabled', False)
                        is_passing = mae_analyzer.is_price_passing_through_mae(
                            ohlc_data,
                            current_price=data.ltp,
                            use_smoothed=use_smoothed
                        )

                        if is_passing:
                            # Calculate MAE value to attach to symbol
                            mae_default, mae_smoothed = mae_analyzer.calculate_mae(ohlc_data)
                            mae_series = mae_smoothed if use_smoothed else mae_default

                            if not mae_series.empty and not mae_series.isna().iloc[-1]:
                                mae_value = round(mae_series.iloc[-1], 2)
                                # Store MAE value in market data for later use
                                data.mae_value = mae_value
                                
                                filtered_data[symbol] = data
                                mae_passed_count += 1
                                logger.debug(f"MAE filter passed for {symbol}: MAE={mae_value}")
                            else:
                                logger.debug(f"MAE filter failed for {symbol}: invalid MAE value")
                        else:
                            logger.debug(f"MAE filter failed for {symbol}: price not passing through MAE")
                    else:
                        logger.debug(f"MAE filter skipped for {symbol}: insufficient OHLC data")

                except Exception as e:
                    logger.warning(f"Error applying MAE filter to {symbol}: {e}")
                    # Include symbol without MAE filtering if there's an error
                    filtered_data[symbol] = data

            logger.info(f"MAE filter: {mae_passed_count}/{len(market_data)} symbols passed")
            return filtered_data

        except Exception as e:
            logger.error(f"Error in MAE filtering: {e}")
            # Return original data if MAE filtering fails
            return market_data

    def apply_pivot_point_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply Pivot Point indicator filter if enabled."""
        if not self.config.pivot_point_enabled:
            logger.debug("Pivot Point indicator is disabled, skipping pivot point filter")
            return market_data

        try:
            # Initialize Pivot Point analyzer with config settings
            pivot_analyzer = PivotPointAnalyzer(
                calculation_type=self.config.pivot_point_calculation_type,
                top_n_closest=self.config.pivot_point_top_n_closest
            )

            filtered_data = {}
            pivot_analyses = []

            for symbol, data in market_data.items():
                try:
                    # Fetch OHLC data for pivot point calculation
                    logger.info(f"Fetching OHLC data for {symbol} for pivot point analysis")
                    ohlc_data = self.fyers_client.get_historical_data(
                        symbol,
                        self.config.timeframe_interval,
                        self.config.days_to_fetch
                    )

                    if ohlc_data and len(ohlc_data) >= 5:  # Need at least 5 periods for pivot calculation
                        # Analyze symbol's proximity to pivot points
                        pivot_analysis = pivot_analyzer.analyze_symbol_proximity_to_pivots(
                            symbol, data.ltp, ohlc_data
                        )

                        if pivot_analysis:
                            # Store pivot analysis in market data
                            data.pivot_analysis = pivot_analysis
                            pivot_analyses.append(pivot_analysis)
                            filtered_data[symbol] = data
                            logger.debug(f"Pivot point analysis completed for {symbol}")
                        else:
                            logger.debug(f"Pivot point analysis failed for {symbol}")
                    else:
                        logger.debug(f"Pivot point analysis skipped for {symbol}: insufficient OHLC data")

                except Exception as e:
                    logger.warning(f"Error applying pivot point analysis to {symbol}: {e}")
                    # Include symbol without pivot point analysis if there's an error
                    filtered_data[symbol] = data

            # Find symbols closest to pivot points and at minimum positive levels
            if pivot_analyses:
                closest_symbols = pivot_analyzer.find_symbols_closest_to_pivots(pivot_analyses)
                min_positive_symbols = pivot_analyzer.find_symbols_at_minimum_positive_pivots(pivot_analyses)

                logger.info(f"Pivot Point analysis: {len(closest_symbols)} closest symbols, {len(min_positive_symbols)} at minimum positive levels")

                # Filter to include only symbols that meet pivot criteria
                pivot_filtered_data = {}
                for symbol, data in filtered_data.items():
                    if hasattr(data, 'pivot_analysis') and data.pivot_analysis:
                        # Include symbols that are either closest to pivots or at minimum positive levels
                        analysis = data.pivot_analysis
                        if (analysis in closest_symbols or analysis in min_positive_symbols):
                            pivot_filtered_data[symbol] = data

                logger.info(f"Pivot Point filter: {len(pivot_filtered_data)}/{len(market_data)} symbols passed")
                return pivot_filtered_data
            else:
                logger.info("No pivot point analyses completed, returning original data")
                return market_data

        except Exception as e:
            logger.error(f"Error in Pivot Point filtering: {e}")
            # Return original data if pivot point filtering fails
            return market_data

    def convert_to_filtered_symbols(self, market_data: Dict[str, MarketData]) -> List[FilteredSymbol]:
        """Convert market data to FilteredSymbol objects."""
        filtered_symbols = []

        for symbol, data in market_data.items():
            # Parse symbol to get metadata
            csv_file = self.config.get_csv_file_for_market_type(self.market_type)
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)

            if parsed_symbol:
                filtered_symbol = FilteredSymbol(
                    symbol=symbol,
                    underlying=parsed_symbol.underlying,
                    market_type=self.market_type,
                    market_data=data,
                    suffix=parsed_symbol.suffix,
                    expiry_year=parsed_symbol.expiry_year,
                    expiry_month=parsed_symbol.expiry_month,
                    strike_price=parsed_symbol.strike_price,
                    option_type=parsed_symbol.option_type
                )

                # Add MAE value if available
                if hasattr(data, 'mae_value'):
                    filtered_symbol.mae_value = data.mae_value

                # Add pivot point analysis if available
                if hasattr(data, 'pivot_analysis'):
                    filtered_symbol.pivot_analysis = data.pivot_analysis

                filtered_symbols.append(filtered_symbol)

        return filtered_symbols
    
    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")
            
            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []
            
            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)
            
            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []
            
            logger.info(f"Fetching market data for {len(symbols_to_scan)} {self.market_type} symbols")
            
            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)
            
            if not market_data:
                logger.warning("No market data received")
                return []
            
            # Update OHLC data with timeframe-specific data
            market_data = self.update_ohlc_with_timeframe_data(market_data)
            
            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            filtered_data = self.apply_mae_filter(filtered_data)
            filtered_data = self.apply_pivot_point_filter(filtered_data)
            
            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)
            
            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols
            
        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class EquityScanner(BaseMarketScanner):
    """Scanner for equity symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'EQUITY')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply equity-specific filters."""
        # No additional filters for equity beyond volume and LTP
        return market_data


class IndexScanner(BaseMarketScanner):
    """Scanner for index symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'INDEX')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply index-specific filters."""
        # No additional filters for index beyond volume and LTP
        return market_data


class FuturesScanner(BaseMarketScanner):
    """Scanner for futures symbols."""
    
    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'FUTURES')
    
    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply futures-specific filters."""
        # No additional filters for futures beyond volume and LTP
        return market_data


class OptionsScanner(BaseMarketScanner):
    """Scanner for options symbols."""

    def __init__(self, config: ConfigLoader):
        super().__init__(config, 'OPTIONS')
        self.options_filter = OptionsChainFilter(config)

    def get_symbols_for_scanning(self, underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Get symbols for scanning with pre-filtering for options to reduce API calls."""
        try:
            # Get all options symbols first
            all_options_symbols = self.symbol_parser.get_symbols_for_market_type(
                self.market_type, underlying_symbols
            )

            logger.info(f"Loaded {len(all_options_symbols)} raw OPTIONS symbols")

            # Apply pre-filtering to reduce symbols before API calls
            if len(all_options_symbols) > 10000:  # Only pre-filter if we have too many symbols
                filtered_symbols = self._apply_pre_filtering(all_options_symbols, underlying_symbols)
                logger.info(f"Pre-filtering reduced symbols from {len(all_options_symbols)} to {len(filtered_symbols)}")
            else:
                filtered_symbols = all_options_symbols

            # Apply delta prefiltering (like reference project approach)
            logger.info(f"Applying delta prefiltering for {len(filtered_symbols)} options symbols...")
            delta_filtered_symbols = self.apply_delta_filter_prefiltering(filtered_symbols)
            logger.info(f"Delta prefiltering reduced symbols from {len(filtered_symbols)} to {len(delta_filtered_symbols)}")

            return delta_filtered_symbols

        except Exception as e:
            logger.error(f"Error in get_symbols_for_scanning: {e}")
            return []

    def _apply_pre_filtering(self, symbols: List[str], underlying_symbols: Optional[List[str]] = None) -> List[str]:
        """Apply pre-filtering to options symbols based on spot prices and strike ranges."""
        try:
            # Parse symbols to UniversalSymbol objects
            csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
            parsed_symbols = []

            for symbol in symbols:
                parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
                if parsed_symbol and parsed_symbol.is_options():
                    parsed_symbols.append(parsed_symbol)

            logger.info(f"Parsed {len(parsed_symbols)} valid options symbols for pre-filtering")

            # Get unique underlying symbols
            unique_underlyings = list(set(s.underlying for s in parsed_symbols))
            logger.info(f"Found {len(unique_underlyings)} unique underlying symbols")

            # Fetch spot prices for underlying symbols
            spot_prices = self.options_filter.get_spot_prices_for_underlyings(unique_underlyings)
            logger.info(f"Retrieved spot prices for {len(spot_prices)} underlying symbols")

            # Apply aggressive pre-filtering to reduce symbols significantly
            max_symbols_per_underlying = 150  # Limit symbols per underlying for API efficiency
            filtered_symbols = self.options_filter.pre_filter_options_symbols(
                parsed_symbols, spot_prices, max_symbols_per_underlying
            )

            # Convert back to NSE symbol strings
            filtered_symbol_strings = [s.get_nse_symbol() for s in filtered_symbols]

            logger.info(f"Pre-filtering completed: {len(filtered_symbol_strings)}/{len(symbols)} symbols selected")
            return filtered_symbol_strings

        except Exception as e:
            logger.error(f"Error in pre-filtering: {e}")
            # Return original symbols if pre-filtering fails
            return symbols

    def apply_delta_filter_prefiltering(self, symbols: List[str]) -> List[str]:
        """Apply delta filtering before fetching market data (prefiltering approach like reference project)."""
        min_delta = self.config.options_min_delta
        max_delta = self.config.options_max_delta

        # If delta filtering is not configured (default values), skip filtering
        if min_delta == 0.0 and max_delta == 1.0:
            logger.debug("Delta filtering not configured, skipping delta prefilter")
            return symbols

        try:
            # Get spot prices for delta calculation
            underlyings = set()
            for symbol in symbols:
                parsed_symbol = self.symbol_parser.parse_symbol(symbol, self.config.get_csv_file_for_market_type('OPTIONS'))
                if parsed_symbol and parsed_symbol.is_options():
                    underlyings.add(parsed_symbol.underlying)

            # Fetch spot prices
            spot_prices = {}
            if underlyings:
                options_filter = OptionsChainFilter(self.config)
                spot_prices = options_filter.get_spot_prices_for_underlyings(list(underlyings))
                logger.info(f"Delta prefilter: Fetched spot prices for {len(spot_prices)} underlyings")

            filtered_symbols = []
            delta_passed_count = 0

            for symbol in symbols:
                try:
                    parsed_symbol = self.symbol_parser.parse_symbol(symbol, self.config.get_csv_file_for_market_type('OPTIONS'))
                    if parsed_symbol and parsed_symbol.is_options():
                        spot_price = spot_prices.get(parsed_symbol.underlying, 0)

                        if spot_price > 0:
                            # Calculate approximate delta based on moneyness
                            # This is a simplified calculation - in practice, you'd use Black-Scholes
                            moneyness = spot_price / parsed_symbol.strike_price

                            if parsed_symbol.option_type == 'CE':
                                # For call options: delta increases as spot > strike
                                estimated_delta = min(0.99, max(0.01, (moneyness - 0.8) / 0.4))
                            else:  # PE
                                # For put options: delta increases as strike > spot
                                estimated_delta = min(0.99, max(0.01, (1.2 - moneyness) / 0.4))

                            # Apply delta filter
                            if min_delta <= estimated_delta <= max_delta:
                                filtered_symbols.append(symbol)
                                delta_passed_count += 1
                                logger.debug(f"Delta prefilter passed for {symbol}: estimated_delta={estimated_delta:.3f}")
                            else:
                                logger.debug(f"Delta prefilter failed for {symbol}: estimated_delta={estimated_delta:.3f}")
                        else:
                            # Include symbol if spot price not available
                            filtered_symbols.append(symbol)
                    else:
                        # Include symbol if parsing fails
                        filtered_symbols.append(symbol)

                except Exception as e:
                    logger.warning(f"Error applying delta prefilter to {symbol}: {e}")
                    # Include symbol if delta filtering fails
                    filtered_symbols.append(symbol)

            logger.info(f"Delta prefilter: {delta_passed_count}/{len(symbols)} symbols passed")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error in delta prefiltering: {e}")
            return symbols

    def apply_delta_filter(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply delta filtering for options if min_delta and max_delta are configured."""
        min_delta = self.config.options_min_delta
        max_delta = self.config.options_max_delta

        # If delta filtering is not configured (default values), skip filtering
        if min_delta == 0.0 and max_delta == 1.0:
            logger.debug("Delta filtering not configured, skipping delta filter")
            return market_data

        try:
            filtered_data = {}
            delta_passed_count = 0

            for symbol, data in market_data.items():
                try:
                    # For now, we'll implement a placeholder delta calculation
                    # In a real implementation, you would fetch delta from options data
                    # This is a simplified approach based on moneyness

                    # Parse symbol to get strike price
                    csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
                    parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)

                    if parsed_symbol and parsed_symbol.strike_price:
                        # Get spot price for underlying
                        spot_prices = self.options_filter.get_spot_prices_for_underlyings([parsed_symbol.underlying])
                        spot_price = spot_prices.get(parsed_symbol.underlying, 0)

                        if spot_price > 0:
                            # Calculate approximate delta based on moneyness
                            # This is a simplified calculation - in practice, you'd use Black-Scholes
                            moneyness = spot_price / parsed_symbol.strike_price

                            if parsed_symbol.option_type == 'CE':
                                # For call options: delta increases as spot > strike
                                estimated_delta = min(0.99, max(0.01, (moneyness - 0.8) / 0.4))
                            else:  # PE
                                # For put options: delta increases as strike > spot
                                estimated_delta = min(0.99, max(0.01, (1.2 - moneyness) / 0.4))

                            # Apply delta filter
                            if min_delta <= estimated_delta <= max_delta:
                                filtered_data[symbol] = data
                                delta_passed_count += 1
                                logger.debug(f"Delta filter passed for {symbol}: estimated_delta={estimated_delta:.3f}")
                            else:
                                logger.debug(f"Delta filter failed for {symbol}: estimated_delta={estimated_delta:.3f}")
                        else:
                            # Include symbol if spot price not available
                            filtered_data[symbol] = data
                    else:
                        # Include symbol if parsing fails
                        filtered_data[symbol] = data

                except Exception as e:
                    logger.warning(f"Error applying delta filter to {symbol}: {e}")
                    # Include symbol if delta filtering fails
                    filtered_data[symbol] = data

            logger.info(f"Delta filter: {delta_passed_count}/{len(market_data)} symbols passed")
            return filtered_data

        except Exception as e:
            logger.error(f"Error in delta filtering: {e}")
            # Return original data if delta filtering fails
            return market_data

    def apply_market_specific_filters(self, market_data: Dict[str, MarketData]) -> Dict[str, MarketData]:
        """Apply options-specific filters including delta filtering."""
        # Apply delta filtering if configured
        filtered_data = self.apply_delta_filter(market_data)

        # Since we already applied pre-filtering, we can apply lighter post-filtering here
        # Convert market data to UniversalSymbol objects for final options filtering
        symbols = []
        csv_file = self.config.get_csv_file_for_market_type('OPTIONS')

        for symbol in filtered_data.keys():
            parsed_symbol = self.symbol_parser.parse_symbol(symbol, csv_file)
            if parsed_symbol:
                symbols.append(parsed_symbol)

        # Apply final options chain filtering (this should be much lighter now)
        filtered_symbols = self.options_filter.filter_options_symbols(symbols)

        # Convert back to market data dictionary
        filtered_data = {}
        filtered_symbol_names = {s.get_nse_symbol() for s in filtered_symbols}

        for symbol, data in market_data.items():
            if symbol in filtered_symbol_names:
                filtered_data[symbol] = data

        logger.info(f"Options chain filter: {len(filtered_data)}/{len(market_data)} symbols passed")
        return filtered_data

    def apply_ce_pe_pairing_on_filtered_symbols(self, filtered_symbols: List[FilteredSymbol]) -> List[FilteredSymbol]:
        """
        Apply CE/PE pairing filter on FilteredSymbol objects that have market data.
        Pairs by underlying, expiry_year, expiry_month, and closest LTP.
        Pair ID format: UnderlyingSymbolYYMMM_PAIRID (e.g., BANKNIFTY25JUL_0001)
        """
        if not self.config.ce_pe_pairing_enabled:
            logger.debug("CE/PE pairing is disabled, skipping pairing filter")
            return filtered_symbols

        min_price_percent = self.config.ce_pe_min_price_percent
        max_price_percent = self.config.ce_pe_max_price_percent

        # Group options by (underlying, expiry_year, expiry_month)
        expiry_groups = defaultdict(lambda: {'CE': [], 'PE': []})
        for symbol in filtered_symbols:
            if symbol.market_type == 'OPTIONS' and symbol.option_type in ['CE', 'PE']:
                key = (symbol.underlying, symbol.expiry_year, symbol.expiry_month)
                expiry_groups[key][symbol.option_type].append(symbol)

        paired_symbols = []
        pair_id_counter = 1
        for (underlying, expiry_year, expiry_month), group in expiry_groups.items():
            ce_list = group['CE']
            pe_list = group['PE']
            # Sort both lists by LTP for deterministic pairing
            ce_list = [s for s in ce_list if s.market_data.ltp > 0]
            pe_list = [s for s in pe_list if s.market_data.ltp > 0]
            ce_list.sort(key=lambda s: s.market_data.ltp)
            pe_list.sort(key=lambda s: s.market_data.ltp)
            used_pe = set()
            for ce in ce_list:
                # Find PE with closest LTP
                closest_pe = None
                min_diff = float('inf')
                for pe in pe_list:
                    if pe.symbol in used_pe:
                        continue
                    price_diff_percent = abs(ce.market_data.ltp - pe.market_data.ltp) / max(ce.market_data.ltp, pe.market_data.ltp) * 100
                    if min_price_percent <= price_diff_percent <= max_price_percent:
                        diff = abs(ce.market_data.ltp - pe.market_data.ltp)
                        if diff < min_diff:
                            min_diff = diff
                            closest_pe = pe
                if closest_pe:
                    # Assign pair ID in required format: UnderlyingSymbolYYMMM_PAIRID
                    yy = str(expiry_year)[-2:] if expiry_year else ''
                    pair_id = f"{underlying}{yy}{expiry_month}_" + f"{pair_id_counter:04d}"
                    ce.pair_id = pair_id
                    closest_pe.pair_id = pair_id
                    paired_symbols.extend([ce, closest_pe])
                    used_pe.add(closest_pe.symbol)
                    pair_id_counter += 1
                    logger.debug(f"Paired {ce.symbol} and {closest_pe.symbol} with pair_id {pair_id} (LTP diff: {min_diff:.2f})")
        logger.info(f"CE/PE pairing filter: {len(paired_symbols)}/{len(filtered_symbols)} symbols paired ({len(paired_symbols)//2} pairs created)")
        return paired_symbols

    def scan_symbols(self) -> List[FilteredSymbol]:
        """Main scanning method with CE/PE pairing applied after market data is available."""
        try:
            logger.info(f"Starting {self.market_type} scanning...")

            # Authenticate
            if not self.authenticate_fyers():
                logger.error("Authentication failed")
                return []

            # Get symbols
            underlying_filter = None if 'ALL' in self.config.symbols else self.config.symbols
            symbols_to_scan = self.get_symbols_for_scanning(underlying_filter)

            if not symbols_to_scan:
                logger.warning(f"No {self.market_type} symbols found for scanning")
                return []

            logger.info(f"Fetching market data for {len(symbols_to_scan)} {self.market_type} symbols")

            # Fetch market data
            market_data = self.fetch_market_data(symbols_to_scan)

            if not market_data:
                logger.warning("No market data received")
                return []

            # Apply filters
            filtered_data = self.apply_volume_filter(market_data)
            filtered_data = self.apply_ltp_filter(filtered_data)
            filtered_data = self.apply_market_specific_filters(filtered_data)
            filtered_data = self.apply_mae_filter(filtered_data)
            filtered_data = self.apply_pivot_point_filter(filtered_data)

            # Convert to FilteredSymbol objects
            filtered_symbols = self.convert_to_filtered_symbols(filtered_data)

            # Apply CE/PE pairing filter if enabled (after market data is available)
            if self.config.ce_pe_pairing_enabled:
                filtered_symbols = self.apply_ce_pe_pairing_on_filtered_symbols(filtered_symbols)

            logger.info(f"{self.market_type} scanning completed: {len(filtered_symbols)} symbols found")
            return filtered_symbols

        except Exception as e:
            logger.error(f"Error during {self.market_type} scanning: {e}")
            return []


class MarketTypeScannerFactory:
    """Factory for creating market type scanners."""
    
    @staticmethod
    def create_scanner(market_type: str, config: ConfigLoader) -> BaseMarketScanner:
        """
        Create a scanner for the specified market type.
        
        Args:
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)
            config: Configuration loader instance
            
        Returns:
            Appropriate scanner instance
        """
        if market_type == 'EQUITY':
            return EquityScanner(config)
        elif market_type == 'INDEX':
            return IndexScanner(config)
        elif market_type == 'FUTURES':
            return FuturesScanner(config)
        elif market_type == 'OPTIONS':
            return OptionsScanner(config)
        else:
            raise ValueError(f"Unknown market type: {market_type}")
    
    @staticmethod
    def get_supported_market_types() -> List[str]:
        """Get list of supported market types."""
        return ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']

