["test_main_functionality.py::test_market_scanners", "test_main_functionality.py::test_options_filtering", "test_main_functionality.py::test_symbol_parsing", "test_main_functionality.py::test_unified_scanner_dry_run", "test_options_prefiltering.py::test_options_prefiltering", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_analyze_symbol_proximity_to_pivots", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_analyzer_initialization", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_calculate_pivot_points", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_find_symbols_at_minimum_positive_pivots", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_find_symbols_closest_to_pivots", "test_pivot_point_indicator.py::TestPivotPointAnalyzer::test_get_period_data_for_calculation", "test_pivot_point_indicator.py::TestPivotPointCSVReporting::test_csv_headers_with_pivot_points", "test_pivot_point_indicator.py::TestPivotPointIntegration::test_filtered_symbol_with_pivot_data", "test_pivot_point_indicator.py::TestPivotPointIntegration::test_pivot_point_filter_disabled", "test_pivot_point_indicator.py::TestPivotPointIntegration::test_pivot_point_filter_enabled"]