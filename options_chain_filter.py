"""
Options Chain Filter for Multi-Market Type Scanner.
Handles filtering of options symbols to create option chains around ATM strikes.
Enhanced with intelligent filtering based on spot prices from EQUITY/INDEX markets.
"""

import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from collections import defaultdict
from universal_symbol_parser import UniversalSymbol
from config_loader import Config<PERSON>oader
from fyers_client import FyersClient, MarketData
from datetime import datetime, timedelta
import calendar

logger = logging.getLogger(__name__)

@dataclass
class OptionsChainData:
    """Data structure for options chain information."""
    underlying: str
    expiry_year: str
    expiry_month: str
    spot_price: Optional[float] = None
    atm_strike: Optional[float] = None
    strike_multiplier: Optional[float] = None
    ce_options: List[UniversalSymbol] = None
    pe_options: List[UniversalSymbol] = None
    
    def __post_init__(self):
        if self.ce_options is None:
            self.ce_options = []
        if self.pe_options is None:
            self.pe_options = []


class OptionsChainFilter:
    """Filter for creating options chains around ATM strikes with intelligent spot price-based filtering."""

    def __init__(self, config: ConfigLoader):
        """
        Initialize the options chain filter.

        Args:
            config: Configuration loader instance
        """
        self.config = config
        self.strike_level = config.options_strike_level
        self.fyers_client = None

        # Cache for spot prices to avoid redundant API calls
        self._spot_price_cache = {}
        self._cache_timestamp = None
        self._cache_expiry_minutes = 5  # Cache expires after 5 minutes

        # Strike interval mapping for different underlyings
        self.strike_intervals = {
            'NIFTY': 50,
            'BANKNIFTY': 100,
            'FINNIFTY': 50,
            'MIDCPNIFTY': 25,
            'SENSEX': 100,
            'BANKEX': 100,
        }

        logger.info(f"Options chain filter initialized with strike level: {self.strike_level}")

    def get_fyers_client(self) -> Optional[FyersClient]:
        """Get or create Fyers client for fetching spot prices."""
        if self.fyers_client is None:
            try:
                # FyersClient expects env_path, not config object
                env_path = self.config.env_path
                self.fyers_client = FyersClient(env_path)
                if not self.fyers_client.authenticate():
                    logger.error("Failed to authenticate Fyers client")
                    return None
            except Exception as e:
                logger.error(f"Error creating Fyers client: {e}")
                return None
        return self.fyers_client

    def _is_cache_valid(self) -> bool:
        """Check if the spot price cache is still valid."""
        if self._cache_timestamp is None:
            return False

        from datetime import datetime, timedelta
        expiry_time = self._cache_timestamp + timedelta(minutes=self._cache_expiry_minutes)
        return datetime.now() < expiry_time

    def _update_cache_timestamp(self):
        """Update the cache timestamp to current time."""
        from datetime import datetime
        self._cache_timestamp = datetime.now()

    def _load_nse_cm_symbols(self):
        """
        Load NSE_CM.csv and cache equity and index symbols for partial matching.
        Returns two lists: equity_symbols, index_symbols (both as tuples of (symbol, nse_symbol)).
        """
        if hasattr(self, '_nse_cm_cache'):
            return self._nse_cm_cache
        import csv
        equity_symbols = []
        index_symbols = []
        try:
            with open('NSE_CM.csv', 'r', encoding='utf-8') as file:
                reader = csv.reader(file)
                for row in reader:
                    if len(row) > 9:
                        nse_symbol = row[9].strip()
                        if nse_symbol.endswith('-EQ'):
                            equity_symbols.append((nse_symbol.replace('NSE:', '').replace('-EQ', ''), nse_symbol))
                        elif nse_symbol.endswith('-INDEX'):
                            index_symbols.append((nse_symbol.replace('NSE:', '').replace('-INDEX', ''), nse_symbol))
        except Exception as e:
            logger.error(f"Error loading NSE_CM.csv for symbol mapping: {e}")
        self._nse_cm_cache = (equity_symbols, index_symbols)
        return equity_symbols, index_symbols

    def get_spot_prices_for_underlyings(self, underlyings: List[str]) -> Dict[str, float]:
        """
        Get spot prices for underlying symbols from EQUITY/INDEX markets, using partial matching in NSE_CM.csv.
        Uses caching to avoid redundant API calls for the same underlying symbols.
        """
        spot_prices = {}

        # Check cache first
        if self._is_cache_valid():
            # Return cached values for requested underlyings
            cached_prices = {}
            missing_underlyings = []

            for underlying in underlyings:
                if underlying in self._spot_price_cache:
                    cached_prices[underlying] = self._spot_price_cache[underlying]
                    logger.debug(f"Using cached spot price for {underlying}: {self._spot_price_cache[underlying]}")
                else:
                    missing_underlyings.append(underlying)

            # If all prices are cached, return them
            if not missing_underlyings:
                logger.info(f"Retrieved all {len(cached_prices)} spot prices from cache")
                return cached_prices

            # If some are cached, start with those and fetch the missing ones
            spot_prices.update(cached_prices)
            underlyings = missing_underlyings
            logger.info(f"Using {len(cached_prices)} cached spot prices, fetching {len(missing_underlyings)} missing")

        # Fetch missing spot prices from API
        client = self.get_fyers_client()
        if not client:
            logger.warning("No Fyers client available for fetching spot prices")
            return spot_prices

        try:
            equity_symbols, index_symbols = self._load_nse_cm_symbols()
            symbols_to_fetch = []
            underlying_to_symbols = {}

            for underlying in underlyings:
                # Try exact match (case-insensitive) in equity
                eq_match = next((nse for base, nse in equity_symbols if base.lower() == underlying.lower()), None)
                # Try exact match (case-insensitive) in index
                idx_match = next((nse for base, nse in index_symbols if base.lower() == underlying.lower()), None)
                # If not found, try partial match (case-insensitive) in equity
                if not eq_match:
                    eq_match = next((nse for base, nse in equity_symbols if underlying.lower() in base.lower()), None)
                # If not found, try partial match (case-insensitive) in index
                if not idx_match:
                    idx_match = next((nse for base, nse in index_symbols if underlying.lower() in base.lower()), None)
                # Decide which symbol to use: prefer index for known indices, else equity
                known_indices = {'NIFTY', 'BANKNIFTY', 'FINNIFTY', 'MIDCPNIFTY', 'SENSEX'}
                selected_symbol = None
                if idx_match and (underlying.upper() in known_indices):
                    selected_symbol = idx_match
                    logger.debug(f"Underlying {underlying} mapped to INDEX symbol {selected_symbol}")
                elif eq_match:
                    selected_symbol = eq_match
                    logger.debug(f"Underlying {underlying} mapped to EQUITY symbol {selected_symbol}")
                elif idx_match:
                    selected_symbol = idx_match
                    logger.debug(f"Underlying {underlying} mapped to INDEX symbol {selected_symbol} (fallback)")
                else:
                    # Fallback to old logic
                    if underlying.upper() in known_indices:
                        selected_symbol = self._get_index_symbol(underlying)
                        logger.debug(f"Underlying {underlying} fallback to INDEX symbol {selected_symbol}")
                    else:
                        selected_symbol = f"NSE:{underlying}-EQ"
                        logger.debug(f"Underlying {underlying} fallback to EQUITY symbol {selected_symbol}")
                underlying_to_symbols[underlying] = selected_symbol
                symbols_to_fetch.append(selected_symbol)

            if not symbols_to_fetch:
                return spot_prices

            logger.info(f"Fetching spot prices for {len(underlyings)} underlyings...")
            market_data = client.get_quotes(symbols_to_fetch)

            # Process fetched data and update cache
            for underlying in underlyings:
                selected_symbol = underlying_to_symbols[underlying]
                if selected_symbol in market_data:
                    price = market_data[selected_symbol].ltp
                    spot_prices[underlying] = price
                    # Update cache
                    self._spot_price_cache[underlying] = price
                    logger.debug(f"Got spot price for {underlying}: {price}")
                else:
                    logger.warning(f"No spot price found for {underlying} (tried {selected_symbol})")

            # Update cache timestamp
            if spot_prices:
                self._update_cache_timestamp()

            logger.info(f"Retrieved spot prices for {len(spot_prices)}/{len(underlyings)} underlyings")

        except Exception as e:
            logger.error(f"Error fetching spot prices: {e}")

        return spot_prices

    def _get_index_symbol(self, underlying: str) -> str:
        """
        Map underlying symbol to correct INDEX symbol format.

        Args:
            underlying: Underlying symbol from options (e.g., 'NIFTY', 'BANKNIFTY')

        Returns:
            Correct INDEX symbol format (e.g., 'NSE:NIFTY50-INDEX')
        """
        # Special mapping for index symbols that don't follow standard pattern
        index_mapping = {
            'NIFTY': 'NSE:NIFTY50-INDEX',
            'BANKNIFTY': 'NSE:NIFTYBANK-INDEX',
            'FINNIFTY': 'NSE:FINNIFTY-INDEX',
            'MIDCPNIFTY': 'NSE:MIDCPNIFTY-INDEX',
            'SENSEX': 'NSE:SENSEX-INDEX',
        }

        # Return mapped symbol if available, otherwise use standard format
        return index_mapping.get(underlying, f"NSE:{underlying}-INDEX")

    def get_strike_interval(self, underlying: str) -> float:
        """
        Get the strike interval for a given underlying symbol.

        Args:
            underlying: Underlying symbol

        Returns:
            Strike interval (e.g., 50 for NIFTY, 100 for BANKNIFTY)
        """
        return self.strike_intervals.get(underlying, 50.0)  # Default to 50

    def calculate_dynamic_strike_range(self, underlying: str, spot_price: float) -> Tuple[float, float]:
        """
        Calculate dynamic strike range based on spot price and strike interval.

        Args:
            underlying: Underlying symbol
            spot_price: Current spot price

        Returns:
            Tuple of (min_strike, max_strike) rounded to strike intervals
        """
        strike_interval = self.get_strike_interval(underlying)
        strike_range = self.strike_level * strike_interval

        # Calculate raw range
        min_strike_raw = spot_price - strike_range
        max_strike_raw = spot_price + strike_range

        # When calculating min_strike and max_strike, keep as int for output, only use rounding for interval alignment
        min_strike = int(round(min_strike_raw / strike_interval) * strike_interval)
        max_strike = int(round(max_strike_raw / strike_interval) * strike_interval)

        logger.debug(f"Dynamic strike range for {underlying} (spot: {spot_price}): "
                    f"{min_strike} - {max_strike} (interval: {strike_interval})")

        return min_strike, max_strike

    def detect_strike_multiplier(self, options: List[UniversalSymbol]) -> float:
        """
        Detect the strike price multiplier for the given options.
        
        Args:
            options: List of options symbols
            
        Returns:
            Detected multiplier (e.g., 50, 100, 250, 500)
        """
        if not options:
            return 50.0  # Default multiplier
        
        # Collect all strike prices
        strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
        
        if len(strikes) < 2:
            return 50.0
        
        # Sort strikes to find differences
        strikes.sort()
        
        # Calculate differences between consecutive strikes
        differences = []
        for i in range(1, len(strikes)):
            diff = strikes[i] - strikes[i-1]
            if diff > 0:
                differences.append(diff)
        
        if not differences:
            return 50.0
        
        # Find the most common difference (mode)
        diff_counts = {}
        for diff in differences:
            diff_counts[diff] = diff_counts.get(diff, 0) + 1
        
        # Get the most frequent difference
        most_common_diff = max(diff_counts.keys(), key=lambda x: diff_counts[x])
        
        logger.debug(f"Detected strike multiplier: {most_common_diff}")
        return most_common_diff
    
    def estimate_atm_strike(self, options: List[UniversalSymbol], 
                           spot_price: Optional[float] = None) -> Optional[float]:
        """
        Estimate the ATM (At The Money) strike price.
        
        Args:
            options: List of options symbols
            spot_price: Current spot price (if available)
            
        Returns:
            Estimated ATM strike price
        """
        if not options:
            return None
        
        # If we have spot price, use it to find nearest strike
        if spot_price is not None:
            strikes = [opt.strike_price for opt in options if opt.strike_price is not None]
            if strikes:
                # Find the strike closest to spot price
                closest_strike = min(strikes, key=lambda x: abs(x - spot_price))
                logger.debug(f"ATM strike based on spot price {spot_price}: {closest_strike}")
                return closest_strike
        
        # Otherwise, use the middle strike as approximation
        strikes = sorted([opt.strike_price for opt in options if opt.strike_price is not None])
        if strikes:
            middle_index = len(strikes) // 2
            atm_strike = strikes[middle_index]
            logger.debug(f"ATM strike estimated from middle: {atm_strike}")
            return atm_strike
        
        return None
    
    def group_options_by_expiry(self, options: List[UniversalSymbol]) -> Dict[Tuple[str, str], List[UniversalSymbol]]:
        """
        Group options by expiry (year + month).
        
        Args:
            options: List of options symbols
            
        Returns:
            Dictionary mapping (year, month) to list of options
        """
        grouped = defaultdict(list)
        
        for option in options:
            if option.is_options() and option.expiry_year and option.expiry_month:
                key = (option.expiry_year, option.expiry_month)
                grouped[key].append(option)
        
        return dict(grouped)

    def get_last_thursday_of_month(self, year: int, month: int) -> datetime:
        """
        Get the last Thursday of a given month (NIFTY/BANKNIFTY/RELIANCE expiry date).

        Args:
            year: Year (e.g., 2024)
            month: Month (1-12)

        Returns:
            datetime object representing the last Thursday of the month
        """
        # Get the last day of the month
        last_day = calendar.monthrange(year, month)[1]

        # Start from the last day and work backwards to find the last Thursday
        for day in range(last_day, 0, -1):
            date = datetime(year, month, day)
            if date.weekday() == 3:  # Thursday is weekday 3
                return date

        # This should never happen, but just in case
        raise ValueError(f"No Thursday found in {year}-{month}")

    def get_target_months_for_filtering(self) -> List[str]:
        """
        Get the target months for filtering. Always returns 3 months.
        If current date has passed this month's expiry (last Thursday),
        consider next 3 months instead of current month + next 2 months.

        Returns:
            List of month abbreviations in format like ['JUL', 'AUG', 'SEP']
        """
        now = datetime.now()
        current_year = now.year
        current_month = now.month

        # Check if current month's expiry has passed
        try:
            current_month_expiry = self.get_last_thursday_of_month(current_year, current_month)
            if now.date() > current_month_expiry.date():
                # Current month expiry has passed, start from next month
                start_month = current_month + 1
                start_year = current_year
                if start_month > 12:
                    start_month = 1
                    start_year += 1
            else:
                # Current month expiry hasn't passed, include current month
                start_month = current_month
                start_year = current_year
        except Exception as e:
            logger.warning(f"Error checking current month expiry: {e}, using current month")
            start_month = current_month
            start_year = current_year

        # Generate 3 months starting from determined start month
        target_months = []
        month = start_month
        year = start_year

        for _ in range(3):
            month_abbr = calendar.month_abbr[month].upper()
            target_months.append(month_abbr)

            month += 1
            if month > 12:
                month = 1
                year += 1

        logger.info(f"Target months for options filtering: {target_months}")
        return target_months

    def filter_options_by_target_months(self, options: List[UniversalSymbol]) -> List[UniversalSymbol]:
        """
        Filter options to include only those with expiry in target months.

        Args:
            options: List of options symbols

        Returns:
            Filtered list of options symbols
        """
        target_months = self.get_target_months_for_filtering()
        filtered_options = []

        for option in options:
            if option.is_options() and option.expiry_month:
                if option.expiry_month in target_months:
                    filtered_options.append(option)

        logger.debug(f"Monthly filter: {len(filtered_options)}/{len(options)} options passed "
                    f"for target months: {target_months}")
        return filtered_options

    def apply_ce_pe_pairing_filter(self, options: List[UniversalSymbol]) -> List[UniversalSymbol]:
        """
        Apply CE/PE pairing filter to match CE and PE options based on strike price, expiry, and price percentage.

        Args:
            options: List of options symbols

        Returns:
            Filtered list of options symbols with pair_id assigned
        """
        if not self.config.ce_pe_pairing_enabled:
            logger.debug("CE/PE pairing is disabled, skipping pairing filter")
            return options

        min_price_percent = self.config.ce_pe_min_price_percent
        max_price_percent = self.config.ce_pe_max_price_percent

        # Group options by underlying, expiry, and strike
        option_groups = defaultdict(lambda: {'CE': None, 'PE': None})

        for option in options:
            if option.is_options() and option.market_data:
                key = (option.underlying, option.expiry_year, option.expiry_month, option.strike_price)
                option_groups[key][option.option_type] = option

        paired_options = []
        pair_id_counter = 1

        for key, group in option_groups.items():
            ce_option = group['CE']
            pe_option = group['PE']

            # Only process if we have both CE and PE options
            if ce_option and pe_option and ce_option.market_data and pe_option.market_data:
                ce_price = ce_option.market_data.ltp
                pe_price = pe_option.market_data.ltp

                # Calculate price percentage difference
                if ce_price > 0 and pe_price > 0:
                    price_diff_percent = abs(ce_price - pe_price) / max(ce_price, pe_price) * 100

                    # Check if price difference is within configured range
                    if min_price_percent <= price_diff_percent <= max_price_percent:
                        # Assign pair ID to both options
                        pair_id = f"PAIR_{pair_id_counter:04d}"
                        ce_option.pair_id = pair_id
                        pe_option.pair_id = pair_id

                        paired_options.extend([ce_option, pe_option])
                        pair_id_counter += 1

                        logger.debug(f"Paired {ce_option.get_nse_symbol()} and {pe_option.get_nse_symbol()} "
                                   f"with pair_id {pair_id} (price diff: {price_diff_percent:.2f}%)")
                    else:
                        logger.debug(f"Price difference {price_diff_percent:.2f}% outside range "
                                   f"[{min_price_percent}, {max_price_percent}] for "
                                   f"{ce_option.get_nse_symbol()} and {pe_option.get_nse_symbol()}")
                else:
                    logger.debug(f"Invalid prices for pairing: CE={ce_price}, PE={pe_price}")

        logger.info(f"CE/PE pairing filter: {len(paired_options)}/{len(options)} options paired "
                   f"({len(paired_options)//2} pairs created)")
        return paired_options

    def filter_options_around_atm(self, options: List[UniversalSymbol],
                                 atm_strike: float,
                                 strike_multiplier: float) -> Tuple[List[UniversalSymbol], List[UniversalSymbol]]:
        """
        Filter options to get CE and PE options around ATM strike.
        
        Args:
            options: List of options symbols
            atm_strike: ATM strike price
            strike_multiplier: Strike price multiplier
            
        Returns:
            Tuple of (CE options, PE options) within strike level range
        """
        # Calculate strike range
        strike_range = self.strike_level * strike_multiplier
        min_strike = atm_strike - strike_range
        max_strike = atm_strike + strike_range
        
        ce_options = []
        pe_options = []
        
        for option in options:
            if (option.strike_price is not None and 
                min_strike <= option.strike_price <= max_strike):
                
                if option.option_type == 'CE':
                    ce_options.append(option)
                elif option.option_type == 'PE':
                    pe_options.append(option)
        
        # Sort by strike price
        ce_options.sort(key=lambda x: x.strike_price)
        pe_options.sort(key=lambda x: x.strike_price)
        
        logger.debug(f"Filtered options around ATM {atm_strike}: {len(ce_options)} CE, {len(pe_options)} PE")
        return ce_options, pe_options

    def filter_options_with_dynamic_range(self, options: List[UniversalSymbol],
                                        underlying: str, spot_price: float) -> Tuple[List[UniversalSymbol], List[UniversalSymbol]]:
        """
        Filter options using dynamic strike range based on spot price and strike intervals.

        Args:
            options: List of options symbols
            underlying: Underlying symbol
            spot_price: Current spot price

        Returns:
            Tuple of (CE options, PE options) within dynamic strike range
        """
        min_strike, max_strike = self.calculate_dynamic_strike_range(underlying, spot_price)

        ce_options = []
        pe_options = []

        for option in options:
            if (option.strike_price is not None and
                min_strike <= option.strike_price <= max_strike):

                if option.option_type == 'CE':
                    ce_options.append(option)
                elif option.option_type == 'PE':
                    pe_options.append(option)

        # Sort by strike price
        ce_options.sort(key=lambda x: x.strike_price)
        pe_options.sort(key=lambda x: x.strike_price)

        logger.debug(f"Dynamic filtered options for {underlying} (spot: {spot_price}): "
                    f"{len(ce_options)} CE, {len(pe_options)} PE in range [{min_strike}, {max_strike}]")
        return ce_options, pe_options

    def create_options_chain(self, underlying: str, options: List[UniversalSymbol],
                           spot_price: Optional[float] = None) -> List[OptionsChainData]:
        """
        Create options chains for the given underlying and options.
        
        Args:
            underlying: Underlying symbol
            options: List of options symbols for this underlying
            spot_price: Current spot price (if available)
            
        Returns:
            List of OptionsChainData objects
        """
        chains = []
        
        # Group options by expiry
        expiry_groups = self.group_options_by_expiry(options)
        
        for (year, month), expiry_options in expiry_groups.items():
            try:
                # Detect strike multiplier for this expiry
                strike_multiplier = self.detect_strike_multiplier(expiry_options)
                
                # Estimate ATM strike
                atm_strike = self.estimate_atm_strike(expiry_options, spot_price)
                
                if atm_strike is None:
                    logger.warning(f"Could not determine ATM strike for {underlying} {year}{month}")
                    continue
                
                # Filter options around ATM
                ce_options, pe_options = self.filter_options_around_atm(
                    expiry_options, atm_strike, strike_multiplier
                )
                
                # Only create chain if we have both CE and PE options
                if ce_options and pe_options:
                    chain = OptionsChainData(
                        underlying=underlying,
                        expiry_year=year,
                        expiry_month=month,
                        spot_price=spot_price,
                        atm_strike=atm_strike,
                        strike_multiplier=strike_multiplier,
                        ce_options=ce_options,
                        pe_options=pe_options
                    )
                    chains.append(chain)
                    
                    logger.debug(f"Created options chain for {underlying} {year}{month}: "
                               f"{len(ce_options)} CE, {len(pe_options)} PE options")
                else:
                    logger.debug(f"Skipping {underlying} {year}{month}: insufficient CE/PE pairs")
                    
            except Exception as e:
                logger.error(f"Error creating options chain for {underlying} {year}{month}: {e}")
                continue
        
        logger.info(f"Created {len(chains)} options chains for {underlying}")
        return chains
    
    def filter_options_symbols(self, options: List[UniversalSymbol],
                             spot_prices: Optional[Dict[str, float]] = None) -> List[UniversalSymbol]:
        """
        Filter options symbols to create option chains around ATM strikes with intelligent spot price-based filtering.

        Args:
            options: List of options symbols
            spot_prices: Optional dictionary of underlying -> spot price

        Returns:
            Filtered list of options symbols that form valid chains
        """
        if not options:
            return []

        # Apply monthly expiry filtering first
        monthly_filtered_options = self.filter_options_by_target_months(options)
        logger.info(f"Monthly expiry filter: {len(monthly_filtered_options)}/{len(options)} options passed")

        # Group options by underlying
        underlying_groups = defaultdict(list)
        for option in monthly_filtered_options:
            if option.is_options():
                underlying_groups[option.underlying].append(option)

        # If no spot prices provided, fetch them from EQUITY/INDEX markets
        if not spot_prices:
            underlyings = list(underlying_groups.keys())
            spot_prices = self.get_spot_prices_for_underlyings(underlyings)
            logger.info(f"Fetched spot prices for {len(spot_prices)} underlyings")

        filtered_options = []

        for underlying, underlying_options in underlying_groups.items():
            try:
                spot_price = spot_prices.get(underlying) if spot_prices else None

                if spot_price:
                    # Use dynamic filtering based on spot price and strike intervals
                    logger.debug(f"Using dynamic filtering for {underlying} with spot price {spot_price}")

                    # Group by expiry and apply dynamic filtering
                    expiry_groups = self.group_options_by_expiry(underlying_options)

                    for (year, month), expiry_options in expiry_groups.items():
                        ce_options, pe_options = self.filter_options_with_dynamic_range(
                            expiry_options, underlying, spot_price
                        )

                        # Only include if we have both CE and PE options
                        if ce_options and pe_options:
                            filtered_options.extend(ce_options)
                            filtered_options.extend(pe_options)
                            logger.debug(f"Added {len(ce_options)} CE and {len(pe_options)} PE options "
                                       f"for {underlying} {year}{month}")
                else:
                    # Fallback to original method if no spot price available
                    logger.debug(f"No spot price for {underlying}, using original filtering")
                    chains = self.create_options_chain(underlying, underlying_options, None)

                    for chain in chains:
                        filtered_options.extend(chain.ce_options)
                        filtered_options.extend(chain.pe_options)

            except Exception as e:
                logger.error(f"Error filtering options for {underlying}: {e}")
                continue

        # Note: CE/PE pairing filter will be applied later after market data is available
        # because it requires LTP prices for pairing logic

        logger.info(f"Enhanced options filter: {len(filtered_options)}/{len(monthly_filtered_options)} options selected")
        return filtered_options

    def pre_filter_options_symbols(self, options: List[UniversalSymbol],
                                 spot_prices: Optional[Dict[str, float]] = None,
                                 max_symbols_per_underlying: int = 200) -> List[UniversalSymbol]:
        """
        Pre-filter options symbols to reduce the number before API calls.
        This is a more aggressive filtering specifically designed to reduce API load.

        Args:
            options: List of options symbols
            spot_prices: Optional dictionary of underlying -> spot price
            max_symbols_per_underlying: Maximum symbols to keep per underlying

        Returns:
            Pre-filtered list of options symbols
        """
        if not options:
            return []

        # Apply monthly expiry filtering first for pre-filtering
        monthly_filtered_options = self.filter_options_by_target_months(options)
        logger.info(f"Pre-filter monthly expiry: {len(monthly_filtered_options)}/{len(options)} options passed")

        # Group options by underlying
        underlying_groups = defaultdict(list)
        for option in monthly_filtered_options:
            if option.is_options():
                underlying_groups[option.underlying].append(option)

        # If no spot prices provided, fetch them
        if not spot_prices:
            underlyings = list(underlying_groups.keys())
            spot_prices = self.get_spot_prices_for_underlyings(underlyings)
            logger.info(f"Pre-filter: Fetched spot prices for {len(spot_prices)} underlyings")

        filtered_options = []

        for underlying, underlying_options in underlying_groups.items():
            try:
                spot_price = spot_prices.get(underlying) if spot_prices else None

                if spot_price:
                    # Use aggressive filtering for pre-filtering
                    underlying_filtered = self._aggressive_filter_by_spot_price(
                        underlying_options, underlying, spot_price, max_symbols_per_underlying
                    )
                    filtered_options.extend(underlying_filtered)
                    logger.debug(f"Pre-filter: {underlying} - {len(underlying_filtered)}/{len(underlying_options)} symbols")
                else:
                    # If no spot price, take only recent expiries and ATM strikes
                    underlying_filtered = self._fallback_aggressive_filter(
                        underlying_options, max_symbols_per_underlying
                    )
                    filtered_options.extend(underlying_filtered)
                    logger.debug(f"Pre-filter (no spot): {underlying} - {len(underlying_filtered)}/{len(underlying_options)} symbols")

            except Exception as e:
                logger.error(f"Error in pre-filtering for {underlying}: {e}")
                continue

        logger.info(f"Pre-filtering completed: {len(filtered_options)}/{len(options)} symbols selected")
        return filtered_options

    def _aggressive_filter_by_spot_price(self, options: List[UniversalSymbol],
                                       underlying: str, spot_price: float,
                                       max_symbols: int) -> List[UniversalSymbol]:
        """Apply aggressive filtering based on spot price for pre-filtering."""
        # Use tighter strike range for pre-filtering (reduce strike_level by half)
        reduced_strike_level = max(5, self.strike_level // 2)  # At least 5 levels

        # Group by expiry
        expiry_groups = self.group_options_by_expiry(options)

        # Sort expiries by date (take only nearest 2-3 expiries for pre-filtering)
        sorted_expiries = sorted(expiry_groups.keys(), key=lambda x: (x[0], x[1]))
        nearest_expiries = sorted_expiries[:3]  # Take only 3 nearest expiries

        filtered_options = []

        for (year, month) in nearest_expiries:
            expiry_options = expiry_groups[(year, month)]

            # Calculate tighter strike range
            strike_interval = self.get_strike_interval(underlying)
            strike_range = reduced_strike_level * strike_interval

            min_strike = spot_price - strike_range
            max_strike = spot_price + strike_range

            # Round to strike intervals
            min_strike = int(round(min_strike / strike_interval) * strike_interval)
            max_strike = int(round(max_strike / strike_interval) * strike_interval)

            ce_options = []
            pe_options = []

            for option in expiry_options:
                if (option.strike_price is not None and
                    min_strike <= option.strike_price <= max_strike):

                    if option.option_type == 'CE':
                        ce_options.append(option)
                    elif option.option_type == 'PE':
                        pe_options.append(option)

            # Only include if we have both CE and PE
            if ce_options and pe_options:
                filtered_options.extend(ce_options)
                filtered_options.extend(pe_options)

        # If still too many symbols, take only strikes closest to spot price
        if len(filtered_options) > max_symbols:
            filtered_options.sort(key=lambda x: abs(x.strike_price - spot_price))
            filtered_options = filtered_options[:max_symbols]

        return filtered_options

    def _fallback_aggressive_filter(self, options: List[UniversalSymbol],
                                  max_symbols: int) -> List[UniversalSymbol]:
        """Fallback aggressive filtering when no spot price is available."""
        # Group by expiry and take only nearest 2 expiries
        expiry_groups = self.group_options_by_expiry(options)
        sorted_expiries = sorted(expiry_groups.keys(), key=lambda x: (x[0], x[1]))
        nearest_expiries = sorted_expiries[:2]

        filtered_options = []

        for (year, month) in nearest_expiries:
            expiry_options = expiry_groups[(year, month)]

            # Take middle range strikes (avoid extreme ITM/OTM)
            strikes = sorted(set(opt.strike_price for opt in expiry_options if opt.strike_price))
            if len(strikes) > 20:
                # Take middle 60% of strikes
                start_idx = len(strikes) // 5
                end_idx = len(strikes) - start_idx
                middle_strikes = set(strikes[start_idx:end_idx])

                for option in expiry_options:
                    if option.strike_price in middle_strikes:
                        filtered_options.append(option)
            else:
                filtered_options.extend(expiry_options)

        # Limit total symbols
        if len(filtered_options) > max_symbols:
            filtered_options = filtered_options[:max_symbols]

        return filtered_options

    def get_options_summary(self, chains: List[OptionsChainData]) -> Dict[str, any]:
        """
        Get summary statistics for options chains.
        
        Args:
            chains: List of options chains
            
        Returns:
            Summary statistics dictionary
        """
        if not chains:
            return {}
        
        total_ce = sum(len(chain.ce_options) for chain in chains)
        total_pe = sum(len(chain.pe_options) for chain in chains)
        unique_underlyings = len(set(chain.underlying for chain in chains))
        
        return {
            'total_chains': len(chains),
            'unique_underlyings': unique_underlyings,
            'total_ce_options': total_ce,
            'total_pe_options': total_pe,
            'total_options': total_ce + total_pe
        }
