"""
Universal Symbol Parser for Multi-Market Type Scanner.
Handles parsing and filtering of EQUITY, INDEX, FUTURES, and OPTIONS symbols.
"""

import csv
import re
import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict
from config_loader import ConfigLoader

logger = logging.getLogger(__name__)

@dataclass
class UniversalSymbol:
    """Universal symbol representation for all market types."""
    symbol: str
    market_type: str  # EQUITY, INDEX, FUTURES, OPTIONS
    underlying: str
    exchange: str = "NSE"
    
    # Market type specific fields
    suffix: Optional[str] = None  # -EQ, -INDEX for equity/index
    expiry_year: Optional[str] = None  # YY for futures/options
    expiry_month: Optional[str] = None  # MMM for futures/options
    strike_price: Optional[float] = None  # Strike for options
    option_type: Optional[str] = None  # CE/PE for options
    
    def get_nse_symbol(self) -> str:
        """Get the full NSE symbol format."""
        return f"{self.exchange}:{self.symbol}"
    
    def get_base_symbol(self) -> str:
        """Get the base symbol without exchange prefix."""
        return self.symbol
    
    def is_equity(self) -> bool:
        """Check if this is an equity symbol."""
        return self.market_type == 'EQUITY'
    
    def is_index(self) -> bool:
        """Check if this is an index symbol."""
        return self.market_type == 'INDEX'
    
    def is_futures(self) -> bool:
        """Check if this is a futures symbol."""
        return self.market_type == 'FUTURES'
    
    def is_options(self) -> bool:
        """Check if this is an options symbol."""
        return self.market_type == 'OPTIONS'


class UniversalSymbolParser:
    """Universal parser for all market types."""
    
    def __init__(self, config: ConfigLoader, target_symbols: List[str]):
        """
        Initialize the universal symbol parser.
        
        Args:
            config: Configuration loader instance
            target_symbols: List of target symbols to filter by
        """
        self.config = config
        self.target_symbols = set(target_symbols) if target_symbols else set()
        self.use_all_symbols = 'ALL' in target_symbols if target_symbols else False
        
        # Compile regex patterns for each market type
        self._compile_patterns()
        
        logger.info(f"Universal symbol parser initialized for {len(self.target_symbols)} target symbols")
        logger.info(f"Use all symbols: {self.use_all_symbols}")
    
    def _compile_patterns(self):
        """Compile regex patterns for all market types."""
        # EQUITY pattern: UNDERLYING-EQ (e.g., RELIANCE-EQ)
        self.equity_pattern = re.compile(r'^([A-Z0-9&]+)-EQ$')
        
        # INDEX pattern: UNDERLYING-INDEX (e.g., NIFTY50-INDEX)
        self.index_pattern = re.compile(r'^([A-Z0-9&]+)-INDEX$')
        
        # FUTURES pattern: UNDERLYINGYYMMMFUT (e.g., RELIANCE25JULFUT)
        self.futures_pattern = re.compile(r'^([A-Z0-9&]+)(\d{2})([A-Z]{3})FUT$')
        
        # OPTIONS pattern: UNDERLYINGYYMMMSTRIKECE/PE (e.g., RELIANCE25JUL2500CE)
        self.options_pattern = re.compile(r'^([A-Z0-9&]+)(\d{2})([A-Z]{3})(\d+(?:\.\d+)?)(CE|PE)$')
        
        logger.debug("Compiled regex patterns for all market types")
    
    def parse_symbol(self, nse_symbol: str, csv_file: str) -> Optional[UniversalSymbol]:
        """
        Parse a symbol from NSE format into UniversalSymbol.
        
        Args:
            nse_symbol: Symbol in NSE format (e.g., NSE:RELIANCE-EQ)
            csv_file: Source CSV file to determine expected market types
            
        Returns:
            UniversalSymbol object or None if parsing fails
        """
        try:
            # Remove NSE: prefix if present
            symbol = nse_symbol.replace('NSE:', '')
            
            # Determine which market types are expected in this CSV
            expected_types = self.config.get_market_types_for_csv(csv_file)
            
            # Try parsing for each expected market type
            for market_type in expected_types:
                parsed_symbol = self._parse_by_market_type(symbol, market_type)
                if parsed_symbol:
                    # Validate underlying symbol if we have specific targets
                    if self.target_symbols and not self.use_all_symbols:
                        if parsed_symbol.underlying not in self.target_symbols:
                            continue
                    return parsed_symbol
            
            return None
            
        except Exception as e:
            logger.debug(f"Error parsing symbol {nse_symbol}: {e}")
            return None
    
    def _parse_by_market_type(self, symbol: str, market_type: str) -> Optional[UniversalSymbol]:
        """Parse symbol for a specific market type."""
        if market_type == 'EQUITY':
            return self._parse_equity(symbol)
        elif market_type == 'INDEX':
            return self._parse_index(symbol)
        elif market_type == 'FUTURES':
            return self._parse_futures(symbol)
        elif market_type == 'OPTIONS':
            return self._parse_options(symbol)
        return None
    
    def _parse_equity(self, symbol: str) -> Optional[UniversalSymbol]:
        """Parse equity symbol."""
        match = self.equity_pattern.match(symbol)
        if match:
            underlying = match.group(1)
            return UniversalSymbol(
                symbol=symbol,
                market_type='EQUITY',
                underlying=underlying,
                suffix='-EQ'
            )
        return None
    
    def _parse_index(self, symbol: str) -> Optional[UniversalSymbol]:
        """Parse index symbol."""
        match = self.index_pattern.match(symbol)
        if match:
            underlying = match.group(1)
            return UniversalSymbol(
                symbol=symbol,
                market_type='INDEX',
                underlying=underlying,
                suffix='-INDEX'
            )
        return None
    
    def _parse_futures(self, symbol: str) -> Optional[UniversalSymbol]:
        """Parse futures symbol."""
        match = self.futures_pattern.match(symbol)
        if match:
            underlying = match.group(1)
            year = match.group(2)
            month = match.group(3)
            return UniversalSymbol(
                symbol=symbol,
                market_type='FUTURES',
                underlying=underlying,
                expiry_year=year,
                expiry_month=month
            )
        return None
    
    def _parse_options(self, symbol: str) -> Optional[UniversalSymbol]:
        """Parse options symbol."""
        match = self.options_pattern.match(symbol)
        if match:
            underlying = match.group(1)
            year = match.group(2)
            month = match.group(3)
            strike = float(match.group(4))
            option_type = match.group(5)
            return UniversalSymbol(
                symbol=symbol,
                market_type='OPTIONS',
                underlying=underlying,
                expiry_year=year,
                expiry_month=month,
                strike_price=strike,
                option_type=option_type
            )
        return None
    
    def load_symbols_from_csv(self, csv_file: str, market_types: List[str], 
                             limit_symbols: Optional[int] = None) -> List[UniversalSymbol]:
        """
        Load symbols from CSV file for specified market types.
        
        Args:
            csv_file: Path to CSV file
            market_types: List of market types to load
            limit_symbols: Optional limit for testing
            
        Returns:
            List of UniversalSymbol objects
        """
        symbols = []
        processed_count = 0
        valid_symbols_count = 0
        
        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)
                
                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        processed_count += 1
                        
                        # Apply limit if specified
                        if limit_symbols and valid_symbols_count >= limit_symbols:
                            break
                        
                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()
                            
                            # Parse the symbol
                            parsed_symbol = self.parse_symbol(nse_symbol, csv_file)
                            
                            if parsed_symbol and parsed_symbol.market_type in market_types:
                                symbols.append(parsed_symbol)
                                valid_symbols_count += 1
                                
                                if valid_symbols_count % 1000 == 0:
                                    logger.debug(f"Loaded {valid_symbols_count} symbols from {csv_file}")
                    
                    except Exception as e:
                        logger.debug(f"Error processing row {row_num} in {csv_file}: {e}")
                        continue
        
        except FileNotFoundError:
            logger.error(f"CSV file not found: {csv_file}")
            return []
        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file}: {e}")
            return []
        
        logger.info(f"Loaded {len(symbols)} symbols from {csv_file} for market types: {market_types}")
        logger.info(f"Performance stats: Processed {processed_count} rows, found {valid_symbols_count} valid symbols")
        
        return symbols
    
    def get_symbols_for_market_type(self, market_type: str, 
                                   underlying_symbols: Optional[List[str]] = None,
                                   limit_symbols: Optional[int] = None) -> List[str]:
        """
        Get symbols for a specific market type ready for scanning.
        
        Args:
            market_type: Market type to get symbols for
            underlying_symbols: Optional list of underlying symbols to filter by
            limit_symbols: Optional limit for testing
            
        Returns:
            List of symbol strings in NSE format
        """
        # Get the CSV file for this market type
        csv_file = self.config.get_csv_file_for_market_type(market_type)
        
        # Load symbols from CSV
        all_symbols = self.load_symbols_from_csv(csv_file, [market_type], limit_symbols)
        
        # Filter by underlying symbols if specified
        if underlying_symbols and not self.use_all_symbols:
            filtered_symbols = [s for s in all_symbols if s.underlying in underlying_symbols]
        else:
            filtered_symbols = all_symbols
        
        # Convert to NSE format strings
        nse_symbols = [s.get_nse_symbol() for s in filtered_symbols]

        logger.info(f"Found {len(nse_symbols)} {market_type} symbols for scanning")
        return nse_symbols

    def get_options_symbols_with_early_filtering(self, underlying_symbols: Optional[List[str]] = None,
                                               limit_symbols: Optional[int] = None,
                                               max_expiries: int = 3) -> List[UniversalSymbol]:
        """
        Get options symbols with early filtering to reduce memory usage and processing time.

        Args:
            underlying_symbols: List of underlying symbols to filter by
            limit_symbols: Maximum number of symbols to load
            max_expiries: Maximum number of expiries to consider per underlying

        Returns:
            List of UniversalSymbol objects for options
        """
        csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
        symbols = []
        processed_count = 0
        valid_symbols_count = 0

        # Track expiries per underlying for early filtering
        underlying_expiries = defaultdict(set)

        logger.info(f"Loading OPTIONS symbols with early filtering (max_expiries: {max_expiries})")

        try:
            with open(csv_file, 'r', encoding='utf-8') as file:
                csv_reader = csv.reader(file)

                for row_num, row in enumerate(csv_reader, 1):
                    try:
                        processed_count += 1

                        # Apply limit if specified
                        if limit_symbols and valid_symbols_count >= limit_symbols:
                            break

                        # Column J (index 9) contains the NSE symbol
                        if len(row) > 9:
                            nse_symbol = row[9].strip()

                            # Parse the symbol
                            parsed_symbol = self.parse_symbol(nse_symbol, csv_file)

                            if parsed_symbol and parsed_symbol.market_type == 'OPTIONS':
                                # Early filtering by underlying
                                if underlying_symbols and not self.use_all_symbols:
                                    if parsed_symbol.underlying not in underlying_symbols:
                                        continue

                                # Early filtering by expiry count
                                expiry_key = (parsed_symbol.expiry_year, parsed_symbol.expiry_month)
                                underlying_key = parsed_symbol.underlying

                                if len(underlying_expiries[underlying_key]) < max_expiries:
                                    underlying_expiries[underlying_key].add(expiry_key)
                                    symbols.append(parsed_symbol)
                                    valid_symbols_count += 1
                                elif expiry_key in underlying_expiries[underlying_key]:
                                    # This expiry is already included for this underlying
                                    symbols.append(parsed_symbol)
                                    valid_symbols_count += 1
                                # Skip if we already have max_expiries for this underlying

                                if valid_symbols_count % 5000 == 0:
                                    logger.debug(f"Loaded {valid_symbols_count} options symbols with early filtering")

                    except Exception as e:
                        logger.debug(f"Error processing row {row_num} in {csv_file}: {e}")
                        continue

        except Exception as e:
            logger.error(f"Error reading CSV file {csv_file}: {e}")
            raise

        logger.info(f"Performance stats with early filtering: Processed {processed_count} rows, "
                   f"found {valid_symbols_count} valid symbols")
        logger.info(f"Loaded {len(underlying_expiries)} underlyings with max {max_expiries} expiries each")

        return symbols
