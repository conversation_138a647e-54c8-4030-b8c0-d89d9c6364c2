# Test Configuration for Options Only with ALL symbols
general:
  env_path: '../.env'
  output_dir: 'reports'
  fyers_api_url: ['https://public.fyers.in/sym_details/NSE_CM.csv', 'https://public.fyers.in/sym_details/NSE_FO.csv']

market_types:
  - OPTIONS

# Trading Symbols - Test with ALL symbols
symbols:
  - 'ALL'

# Market Filter Settings
market_filters:
  min_volume: 0
  max_volume: 10000000000
  min_ltp_price: 0
  max_ltp_price: 100000.0

# Options Filter Settings
options_filter:
  strike_level: 20    # Strike level from ATM for both CE and PE

# Timeframe Settings
timeframe:
  interval: 60
  days_to_fetch: 10

ce_pe_pairing:
  enabled: false
  min_price_percent: 0.0
  max_price_percent: 2.0

# Moving Average Exponential Indicator Settings
mae_indicator:
  enabled: false
  length: 9
  source: 'close'
  offset: 0
  smoothing_enabled: false
  smoothing_line: 'sma'
  smoothing_length: 9

# Rate Limiting Settings
rate_limit:
  min_delay_seconds: 0.1
  max_retries: 5
  retry_backoff: 3.0
