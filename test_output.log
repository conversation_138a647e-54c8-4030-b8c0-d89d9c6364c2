============================= test session starts =============================
platform win32 -- Python 3.10.9, pytest-8.3.5, pluggy-1.5.0 -- C:\Users\<USER>\AppData\Local\Programs\Python\Python310\python.exe
cachedir: .pytest_cache
rootdir: C:\Users\<USER>\Desktop\Python\multitimeframe_scanner
plugins: cov-6.1.1
collecting ... collected 5 items

test_main_functionality.py::test_symbol_parsing PASSED                   [ 20%]
test_main_functionality.py::test_market_scanners PASSED                  [ 40%]
test_main_functionality.py::test_options_filtering PASSED                [ 60%]
test_main_functionality.py::test_unified_scanner_dry_run PASSED          [ 80%]
test_options_prefiltering.py::test_options_prefiltering PASSED           [100%]

======================== 5 passed, 1 warning in 7.53s =========================
