"""
Test script to run the reference-style processor and compare with reference project.
"""

import logging
import sys
from datetime import datetime

from config_loader import get_config
from reference_style_processor import ReferenceStyleProcessor
from fyers_config import setup_logging

def main():
    """Test the reference-style processor."""
    
    # Setup logging
    logger = setup_logging(level=logging.INFO, log_to_file=True)
    
    try:
        logger.info("=" * 80)
        logger.info("REFERENCE STYLE PROCESSOR TEST")
        logger.info("=" * 80)
        logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Load configuration
        config = get_config()
        
        # Initialize reference-style processor
        processor = ReferenceStyleProcessor(config)
        
        # Process NIFTY symbol (like reference project)
        symbol = 'NIFTY'
        logger.info(f"Processing symbol: {symbol}")
        
        reports = processor.process_symbol(symbol)
        
        if reports:
            logger.info(f"Successfully generated reports for {symbol}:")
            for report_type, filepath in reports.items():
                if filepath:
                    logger.info(f"  {report_type}: {filepath}")
        else:
            logger.warning(f"No reports generated for {symbol}")
        
        logger.info("=" * 80)
        logger.info("REFERENCE STYLE PROCESSOR TEST COMPLETED")
        logger.info("=" * 80)
        
        return True
        
    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        print("\nTest interrupted by user")
        return False

    except Exception as e:
        logger.error(f"Test failed with error: {e}", exc_info=True)
        print(f"\nTest failed: {e}")
        print("Check the log files for detailed error information")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
