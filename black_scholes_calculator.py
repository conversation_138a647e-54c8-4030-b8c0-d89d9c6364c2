"""
Black-Scholes Option Pricing and Greeks Calculator.
Provides accurate delta calculations and implied volatility for options filtering.
"""

import logging
import math
import numpy as np
from scipy.stats import norm
from scipy.optimize import brentq
from typing import Optional, Tuple
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

class BlackScholesCalculator:
    """Black-Scholes calculator for option pricing and Greeks."""
    
    def __init__(self, risk_free_rate: float = 0.05):
        """
        Initialize Black-Scholes calculator.
        
        Args:
            risk_free_rate: Risk-free interest rate (default 5%)
        """
        self.risk_free_rate = risk_free_rate
        
    def black_scholes_price(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str) -> float:
        """
        Calculate Black-Scholes option price for CE and PE.
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            option_type: 'CE' for call, 'PE' for put
            
        Returns:
            Option price
        """
        try:
            if T <= 0 or sigma <= 0:
                return 0.0
                
            sqrtT = np.sqrt(T)
            lnSK = np.log(S / K)
            d1 = (lnSK + (r + 0.5 * sigma ** 2) * T) / (sigma * sqrtT)
            d2 = d1 - sigma * sqrtT

            if option_type.upper() == 'CE':
                return S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
            elif option_type.upper() == 'PE':
                return K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
            else:
                raise ValueError("option_type must be 'CE' or 'PE'")
                
        except Exception as e:
            logger.warning(f"Error calculating Black-Scholes price: {e}")
            return 0.0

    def implied_volatility(self, symbol: str, price: float, S: float, K: float, T: float, r: float, option_type: str) -> float:
        """
        Calculate implied volatility from market price.
        
        Args:
            symbol: Option symbol for logging
            price: Market price of option
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            option_type: 'CE' for call, 'PE' for put
            
        Returns:
            Implied volatility
        """
        def objective(sigma):
            return self.black_scholes_price(S, K, T, r, sigma, option_type) - price
            
        try:
            # Ensure T is not zero or negative for brentq
            if T <= 0:
                logger.warning(f"Invalid time to expiration T={T} for {symbol}. Returning default IV.")
                return 0.0001  # Return a small positive value if T is invalid
                
            if price <= 0:
                logger.warning(f"Invalid price={price} for {symbol}. Returning default IV.")
                return 0.2
                
            return brentq(objective, 1e-5, 5.0)
            
        except ValueError as e:
            logger.warning(f"Could not find implied volatility for symbol={symbol}, price={price}, S={S}, K={K}, T={T}, r={r}, option_type={option_type}: {e}. Returning default IV.")
            return 0.2  # Return a default implied volatility (e.g., 20%) if brentq fails

    def black_scholes_delta(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str) -> float:
        """
        Calculate Black-Scholes delta (price sensitivity to underlying price change).
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            option_type: 'CE' for call, 'PE' for put
            
        Returns:
            Delta value
        """
        logger.debug(f"BS Delta Inputs: S={S}, K={K}, T={T}, r={r}, sigma={sigma}, type={option_type}")
        
        try:
            if T <= 0 or sigma <= 0 or np.isnan(sigma):
                logger.warning(f"Invalid T or sigma for Black-Scholes Delta. T={T}, sigma={sigma}. Returning 0.0")
                return 0.0

            d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))

            if option_type.upper() == 'CE':
                return norm.cdf(d1)
            elif option_type.upper() == 'PE':
                return norm.cdf(d1) - 1
            else:
                raise ValueError("option_type must be 'CE' or 'PE'")
                
        except Exception as e:
            logger.warning(f"Error calculating Black-Scholes delta: {e}")
            return 0.0

    def calculate_time_to_expiration(self, expiry_date: str) -> float:
        """
        Calculate time to expiration in years.
        
        Args:
            expiry_date: Expiry date in format like '25JUL' or '2025-07-25'
            
        Returns:
            Time to expiration in years
        """
        try:
            current_date = datetime.now()
            
            # Handle different date formats
            if len(expiry_date) == 5 and expiry_date[2:].isalpha():  # Format like '25JUL'
                day = int(expiry_date[:2])
                month_str = expiry_date[2:]
                year = current_date.year
                
                # Month mapping
                month_map = {
                    'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
                    'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
                }
                
                month = month_map.get(month_str, current_date.month)
                
                # If the month has passed, assume next year
                if month < current_date.month or (month == current_date.month and day < current_date.day):
                    year += 1
                    
                expiry_datetime = datetime(year, month, day)
                
            else:
                # Try to parse as standard date format
                expiry_datetime = datetime.strptime(expiry_date, '%Y-%m-%d')
            
            # Calculate time difference in years
            time_diff = expiry_datetime - current_date
            time_to_expiration = time_diff.total_seconds() / (365.25 * 24 * 3600)  # Convert to years
            
            return max(time_to_expiration, 1/365.25)  # Minimum 1 day
            
        except Exception as e:
            logger.warning(f"Error calculating time to expiration for {expiry_date}: {e}")
            return 30/365.25  # Default to 30 days

    def calculate_accurate_delta(self, symbol: str, market_price: float, spot_price: float, 
                               strike_price: float, expiry_date: str, option_type: str) -> float:
        """
        Calculate accurate delta using Black-Scholes with implied volatility.
        
        Args:
            symbol: Option symbol
            market_price: Current market price of option
            spot_price: Current spot price of underlying
            strike_price: Strike price of option
            expiry_date: Expiry date
            option_type: 'CE' or 'PE'
            
        Returns:
            Accurate delta value
        """
        try:
            # Calculate time to expiration
            T = self.calculate_time_to_expiration(expiry_date)
            
            # Calculate implied volatility from market price
            iv = self.implied_volatility(symbol, market_price, spot_price, strike_price, T, self.risk_free_rate, option_type)
            
            # Calculate delta using Black-Scholes
            delta = self.black_scholes_delta(spot_price, strike_price, T, self.risk_free_rate, iv, option_type)
            
            # Return absolute value for filtering purposes
            return abs(delta)
            
        except Exception as e:
            logger.warning(f"Error calculating accurate delta for {symbol}: {e}")
            # Fallback to simplified delta calculation
            return self._simplified_delta_calculation(spot_price, strike_price, option_type)

    def _simplified_delta_calculation(self, spot_price: float, strike_price: float, option_type: str) -> float:
        """
        Simplified delta calculation as fallback.
        
        Args:
            spot_price: Current spot price
            strike_price: Strike price
            option_type: 'CE' or 'PE'
            
        Returns:
            Simplified delta estimate
        """
        try:
            moneyness = spot_price / strike_price
            
            if option_type.upper() == 'CE':
                # For call options: delta increases as spot > strike
                estimated_delta = min(0.99, max(0.01, (moneyness - 0.8) / 0.4))
            else:  # PE
                # For put options: delta increases as strike > spot
                estimated_delta = min(0.99, max(0.01, (1.2 - moneyness) / 0.4))
            
            return estimated_delta
            
        except Exception as e:
            logger.warning(f"Error in simplified delta calculation: {e}")
            return 0.5  # Default delta

    def calculate_gamma(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """
        Calculate gamma (rate of change of delta).
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            
        Returns:
            Gamma value
        """
        try:
            if T <= 0 or sigma <= 0:
                return 0.0
                
            d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * math.sqrt(T))
            gamma = norm.pdf(d1) / (S * sigma * math.sqrt(T))
            
            return gamma
            
        except Exception as e:
            logger.warning(f"Error calculating gamma: {e}")
            return 0.0

    def calculate_theta(self, S: float, K: float, T: float, r: float, sigma: float, option_type: str) -> float:
        """
        Calculate theta (time decay).
        
        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            option_type: 'CE' for call, 'PE' for put
            
        Returns:
            Theta value
        """
        try:
            if T <= 0 or sigma <= 0:
                return 0.0
                
            sqrtT = math.sqrt(T)
            d1 = (math.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * sqrtT)
            d2 = d1 - sigma * sqrtT
            
            if option_type.upper() == 'CE':
                theta = (-S * norm.pdf(d1) * sigma / (2 * sqrtT) - 
                        r * K * math.exp(-r * T) * norm.cdf(d2))
            else:  # PE
                theta = (-S * norm.pdf(d1) * sigma / (2 * sqrtT) + 
                        r * K * math.exp(-r * T) * norm.cdf(-d2))
            
            return theta / 365.25  # Convert to daily theta
            
        except Exception as e:
            logger.warning(f"Error calculating theta: {e}")
            return 0.0
