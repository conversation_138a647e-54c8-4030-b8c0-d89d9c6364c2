"""
Comprehensive tests for Pivot Point Indicator functionality.
Tests pivot point calculations, proximity analysis, and integration with market scanners.
"""

import pytest
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from typing import List, Dict

from pivot_point_indicator import PivotPointAnalyzer, PivotPointLevels, PivotPointAnalysis
from config_loader import ConfigLoader
from market_type_scanner import MarketData, FilteredSymbol

# Setup logging for tests
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

class TestPivotPointAnalyzer:
    """Test cases for PivotPointAnalyzer class."""
    
    def setup_method(self):
        """Setup test fixtures."""
        self.analyzer = PivotPointAnalyzer(calculation_type='WEEKLY', top_n_closest=5)
        
        # Mock OHLC data
        self.mock_ohlc_data = []
        for i in range(10):
            mock_candle = Mock()
            mock_candle.high = 25000 + (i * 10)
            mock_candle.low = 24900 + (i * 10)
            mock_candle.close = 24950 + (i * 10)
            mock_candle.datetime = datetime.now()
            self.mock_ohlc_data.append(mock_candle)
    
    def test_analyzer_initialization(self):
        """Test analyzer initialization with different parameters."""
        # Test valid initialization
        analyzer = PivotPointAnalyzer('DAILY', 3)
        assert analyzer.calculation_type == 'DAILY'
        assert analyzer.top_n_closest == 3
        
        # Test invalid calculation type
        with pytest.raises(ValueError):
            PivotPointAnalyzer('INVALID', 5)
    
    def test_calculate_pivot_points(self):
        """Test pivot point calculation with known values."""
        high = 25000.0
        low = 24800.0
        close = 24900.0
        
        pivot_levels = self.analyzer.calculate_pivot_points(high, low, close)
        
        # Test basic pivot calculation
        expected_pivot = (high + low + close) / 3
        assert pivot_levels.pivot == round(expected_pivot, 2)
        
        # Test resistance levels
        assert 'R1' in pivot_levels.resistance_levels
        assert 'R11' in pivot_levels.resistance_levels
        assert len(pivot_levels.resistance_levels) == 11
        
        # Test support levels
        assert 'S1' in pivot_levels.support_levels
        assert 'S11' in pivot_levels.support_levels
        assert len(pivot_levels.support_levels) == 11
        
        # Test calculation type and date
        assert pivot_levels.calculation_type == 'WEEKLY'
        assert pivot_levels.calculation_date is not None
    
    def test_get_period_data_for_calculation(self):
        """Test extraction of period data for different calculation types."""
        # Test DAILY calculation
        daily_analyzer = PivotPointAnalyzer('DAILY', 5)
        high, low, close = daily_analyzer.get_period_data_for_calculation(self.mock_ohlc_data)
        
        # Should use last candle's data
        assert high == self.mock_ohlc_data[-1].high
        assert low == self.mock_ohlc_data[-1].low
        assert close == self.mock_ohlc_data[-1].close
        
        # Test WEEKLY calculation
        weekly_analyzer = PivotPointAnalyzer('WEEKLY', 5)
        high, low, close = weekly_analyzer.get_period_data_for_calculation(self.mock_ohlc_data)
        
        # Should use max high, min low from recent data
        recent_data = self.mock_ohlc_data[-7:]
        expected_high = max(candle.high for candle in recent_data)
        expected_low = min(candle.low for candle in recent_data)
        expected_close = recent_data[-1].close
        
        assert high == expected_high
        assert low == expected_low
        assert close == expected_close
    
    def test_analyze_symbol_proximity_to_pivots(self):
        """Test symbol proximity analysis to pivot points."""
        symbol = "NSE:NIFTY50-INDEX"
        current_ltp = 24950.0
        
        analysis = self.analyzer.analyze_symbol_proximity_to_pivots(
            symbol, current_ltp, self.mock_ohlc_data
        )
        
        assert analysis is not None
        assert analysis.symbol == symbol
        assert analysis.current_ltp == current_ltp
        assert analysis.pivot_levels is not None
        assert analysis.closest_pivot_level is not None
        assert analysis.closest_pivot_value > 0
        assert analysis.distance_to_closest >= 0
        assert analysis.distance_percent >= 0
        assert len(analysis.top_n_closest) <= 5
    
    def test_find_symbols_closest_to_pivots(self):
        """Test finding symbols closest to pivot points."""
        # Create mock analyses
        analyses = []
        for i in range(10):
            analysis = Mock(spec=PivotPointAnalysis)
            analysis.symbol = f"SYMBOL_{i}"
            analysis.distance_to_closest = i * 10.0  # Different distances
            analyses.append(analysis)
        
        closest_symbols = self.analyzer.find_symbols_closest_to_pivots(analyses)
        
        # Should return top 5 closest symbols
        assert len(closest_symbols) == 5
        # Should be sorted by distance (ascending)
        for i in range(1, len(closest_symbols)):
            assert closest_symbols[i-1].distance_to_closest <= closest_symbols[i].distance_to_closest
    
    def test_find_symbols_at_minimum_positive_pivots(self):
        """Test finding symbols at minimum positive pivot levels."""
        # Create mock analyses
        analyses = []
        for i in range(5):
            analysis = Mock(spec=PivotPointAnalysis)
            analysis.symbol = f"SYMBOL_{i}"
            analysis.distance_to_closest = (i + 1) * 5.0
            analysis.is_positive_minimum = i < 3  # First 3 are at minimum positive
            analyses.append(analysis)
        
        min_positive_symbols = self.analyzer.find_symbols_at_minimum_positive_pivots(analyses)
        
        # Should return only symbols at minimum positive levels
        assert len(min_positive_symbols) == 3
        for symbol in min_positive_symbols:
            assert symbol.is_positive_minimum is True


class TestPivotPointIntegration:
    """Test integration of pivot point indicator with market scanners."""
    
    def setup_method(self):
        """Setup test fixtures for integration tests."""
        # Mock config
        self.mock_config = Mock(spec=ConfigLoader)
        self.mock_config.pivot_point_enabled = True
        self.mock_config.pivot_point_calculation_type = 'WEEKLY'
        self.mock_config.pivot_point_top_n_closest = 5
        self.mock_config.timeframe_interval = 60
        self.mock_config.days_to_fetch = 10
        self.mock_config.symbols = ['NIFTY50', 'BANKNIFTY']
        
        # Mock market data
        self.mock_market_data = {
            'NSE:NIFTY50-INDEX': MarketData(
                symbol='NSE:NIFTY50-INDEX',
                ltp=24950.0,
                volume=1000000
            ),
            'NSE:BANKNIFTY-INDEX': MarketData(
                symbol='NSE:BANKNIFTY-INDEX',
                ltp=51500.0,
                volume=800000
            )
        }
    
    @patch('market_type_scanner.PivotPointAnalyzer')
    @patch('market_type_scanner.FyersClient')
    def test_pivot_point_filter_enabled(self, mock_fyers_client, mock_pivot_analyzer):
        """Test pivot point filter when enabled."""
        from market_type_scanner import IndexScanner

        # Setup mocks
        mock_analyzer_instance = Mock()
        mock_pivot_analyzer.return_value = mock_analyzer_instance

        mock_analysis = Mock(spec=PivotPointAnalysis)
        mock_analysis.symbol = 'NSE:NIFTY50-INDEX'
        mock_analysis.distance_to_closest = 10.0
        mock_analyzer_instance.analyze_symbol_proximity_to_pivots.return_value = mock_analysis
        mock_analyzer_instance.find_symbols_closest_to_pivots.return_value = [mock_analysis]
        mock_analyzer_instance.find_symbols_at_minimum_positive_pivots.return_value = [mock_analysis]

        # Mock OHLC data
        mock_ohlc_data = [Mock() for _ in range(10)]
        mock_fyers_instance = Mock()
        mock_fyers_instance.get_historical_data.return_value = mock_ohlc_data

        # Create scanner instance
        scanner = IndexScanner(self.mock_config)
        scanner.fyers_client = mock_fyers_instance

        # Test pivot point filter
        filtered_data = scanner.apply_pivot_point_filter(self.mock_market_data)

        # Verify analyzer was called
        mock_pivot_analyzer.assert_called_once()
        mock_analyzer_instance.analyze_symbol_proximity_to_pivots.assert_called()

        # Should return filtered data
        assert len(filtered_data) <= len(self.mock_market_data)
    
    def test_pivot_point_filter_disabled(self):
        """Test pivot point filter when disabled."""
        from market_type_scanner import IndexScanner

        # Disable pivot point indicator
        self.mock_config.pivot_point_enabled = False

        scanner = IndexScanner(self.mock_config)
        filtered_data = scanner.apply_pivot_point_filter(self.mock_market_data)

        # Should return original data unchanged
        assert filtered_data == self.mock_market_data
    
    def test_filtered_symbol_with_pivot_data(self):
        """Test FilteredSymbol with pivot point analysis data."""
        # Create mock pivot analysis
        mock_analysis = Mock(spec=PivotPointAnalysis)
        mock_analysis.closest_pivot_level = 'R1'
        mock_analysis.closest_pivot_value = 25000.0
        mock_analysis.distance_to_closest = 50.0
        mock_analysis.distance_percent = 0.2
        mock_analysis.is_positive_minimum = True
        
        # Create market data with pivot analysis
        market_data = MarketData(
            symbol='NSE:NIFTY50-INDEX',
            ltp=24950.0,
            volume=1000000
        )
        market_data.pivot_analysis = mock_analysis
        
        # Create filtered symbol
        filtered_symbol = FilteredSymbol(
            symbol='NSE:NIFTY50-INDEX',
            underlying='NIFTY50',
            market_type='INDEX',
            market_data=market_data
        )
        filtered_symbol.pivot_analysis = mock_analysis
        
        # Verify pivot data is accessible
        assert filtered_symbol.pivot_analysis == mock_analysis
        assert filtered_symbol.pivot_analysis.closest_pivot_level == 'R1'


class TestPivotPointCSVReporting:
    """Test CSV reporting with pivot point data."""
    
    def setup_method(self):
        """Setup test fixtures for CSV reporting tests."""
        self.mock_config = Mock(spec=ConfigLoader)
        self.mock_config.pivot_point_enabled = True
        self.mock_config.mae_enabled = False
        self.mock_config.symbols = ['NIFTY50', 'BANKNIFTY']  # Add symbols for ReportGenerator
        
    def test_csv_headers_with_pivot_points(self):
        """Test CSV headers include pivot point columns when enabled."""
        from report_generator import ReportGenerator
        
        # Create mock filtered symbols
        mock_analysis = Mock(spec=PivotPointAnalysis)
        mock_analysis.closest_pivot_level = 'R1'
        mock_analysis.closest_pivot_value = 25000.0
        mock_analysis.distance_to_closest = 50.0
        mock_analysis.distance_percent = 0.2
        mock_analysis.is_positive_minimum = True
        
        market_data = MarketData(
            symbol='NSE:NIFTY50-INDEX',
            ltp=24950.0,
            volume=1000000
        )
        
        filtered_symbol = FilteredSymbol(
            symbol='NSE:NIFTY50-INDEX',
            underlying='NIFTY50',
            market_type='INDEX',
            market_data=market_data
        )
        filtered_symbol.pivot_analysis = mock_analysis
        
        # Test report generation (mock file operations)
        with patch('builtins.open', create=True) as mock_open:
            with patch('csv.writer') as mock_csv_writer:
                mock_writer = Mock()
                mock_csv_writer.return_value = mock_writer
                
                report_generator = ReportGenerator(config=self.mock_config)
                report_generator.create_csv_report([filtered_symbol], 'test_report.csv')
                
                # Verify headers include pivot point columns
                headers_call = mock_writer.writerow.call_args_list[0][0][0]
                assert 'closest_pivot_level' in headers_call
                assert 'closest_pivot_value' in headers_call
                assert 'distance_to_closest' in headers_call
                assert 'distance_percent' in headers_call
                assert 'is_positive_minimum' in headers_call


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
